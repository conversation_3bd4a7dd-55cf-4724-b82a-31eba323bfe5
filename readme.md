
# AI论文写作平台

## 项目概述
基于 **thinkadmin + n8n** 架构的智能论文写作平台，提供AIGC语义降重和第三方论文查重集成服务。
面向用户为高校学生、研究人员，支持多种AI模型接入与个性化写作控制。

## 🏗️ 整体架构设计

### 🔧 thinkadmin 的角色
- **用户系统管理**：用户注册、登录、权限控制、VIP管理
- **内容管理**：论文模板、提示词模板、用户草稿管理
- **数据库管理**：用户数据、任务记录、模板配置、订单记录
- **前端控制台**：作为管理后台和用户操作界面，调用API，展示任务执行结果
- **支付系统**：订单管理、套餐配置

### 🔄 n8n 的角色（流程编排引擎）
- **写作流程编排**：
  ```
  用户请求写作 → 调用AI模型 → 存储结果 → 字数检查 → 格式化处理 → 通知完成
  ```
- **降重流程编排**：
  ```
  上传文档 → 文本提取 → AI语义改写 → 对比分析 → 重复率预估 → 结果返回
  ```
- **查重流程编排**：
  ```
  提交文档 → 调用查重API → 等待结果 → 解析报告 → 生成建议 → 状态更新
  ```
- **多服务集成**：OpenAI、百度文心、阿里通义、查重接口、邮箱通知、文件存储
- **异步任务处理**：分阶段任务执行、回调处理、错误重试机制
## 🧩 核心功能模块

### 📄 写作中心模块
**thinkadmin 负责**：
- 论文类型管理（毕业论文、期刊论文、项目建议书等）✅ 已实现
- 大纲模板管理（维护各类论文的章节结构模板）✅ 已实现
- 提示词模板管理（管理写作过程中使用的 AI 提示词）⚠️ 数据表已建，控制器待开发
- 正文模板管理（设置生成正文的格式样式、段落间距、引用规则）❌ 待开发
- 写作任务管理（查看用户提交的写作任务，支持重试、状态跟踪）⚠️ 基础功能已实现，需完善
- 草稿箱管理（用户草稿版本的查看、编辑与恢复）❌ 待开发

**n8n 工作流负责**：
- 智能提纲生成流程：`用户输入 → 模板匹配 → AI调用 → 结果处理 → 存储`
- 全文生成流程：`大纲解析 → 分段处理 → AI写作 → 字数控制 → 文风调整 → 合并输出`
- 分段生成控制：6000字 ~ 30000字，支持分批处理避免超时
- 多模型切换与负载均衡

### 🔄 降重与查重模块
**thinkadmin 负责**：
- 降重记录管理（展示用户提交的降重记录，支持对比原文与改写结果）❌ 待开发
- 降重模型配置（配置降重所使用的 AI 模型及其优先级、负载方式）❌ 待开发
- 查重记录管理（查看查重任务状态、结果报告与重复率预估）❌ 待开发
- 查重接口配置（配置维普、知网、万方等查重服务的接口和凭据）❌ 待开发

**n8n 工作流负责**：
- 文档处理流程：`文件上传 → 格式识别 → 文本提取 → 分段处理`
- AI降重流程：`文本分析 → 多模型并行调用 → 语义改写 → 原意保持检查`
- 重复率预估：`降重文本 → 简单查重算法 → 预估重复率 → 结果反馈`
- 查重提交流程：`文档上传 → 格式转换 → API调用 → 任务ID获取`
- 结果轮询流程：`定时检查 → 状态查询 → 报告下载 → 解析处理`

### 📁 文档导出模块
**thinkadmin 负责**：
- 导出样式模板（配置 DOCX、PDF 的导出模板、封面格式、字体）❌ 待开发
- 下载记录管理（用户导出行为记录，便于内容溯源与权限控制）❌ 待开发
- 导出任务监控（查看导出流程的状态、是否失败、是否打包成功）❌ 待开发

**n8n 工作流负责**：
- 文档生成流程：`内容整理 → 格式转换 → 封面生成 → 目录生成 → 样式应用`
- 批量导出：`多文档处理 → 压缩打包 → 下载链接生成`
- 格式化处理：按标准论文排版样式自动格式化

### 👤 用户中心模块
**thinkadmin 核心功能**：
- 用户列表（基础信息、VIP等级、配额、最近登录时间）✅ 可使用ThinkAdmin内置用户管理
- VIP 套餐管理（定义各套餐的价格、字数上限、权限范围）⚠️ 可集成account插件
- 用户积分管理（记录用户充值与消耗积分的明细）⚠️ 可集成payment插件
- 历史项目管理界面
- 云端草稿管理与版本控制

### 💰 收费系统模块
**thinkadmin 核心功能**：
- 订单管理（查看和管理所有付费订单，含状态、金额）⚠️ 可集成payment插件
- 套餐配置（编辑套餐销售内容、定价策略）⚠️ 可集成payment插件
- 充值记录（用户充值记录日志，支持导出）⚠️ 可集成payment插件
- 发票管理（电子发票的申请、开具与审核）❌ 待开发
- 微信/支付宝支付集成
- 支付回调处理

### 📬 通知与消息模块
**thinkadmin 核心功能**：
- 系统通知记录（系统向用户推送的所有通知消息）❌ 待开发
- 消息模板管理（配置任务完成、查重成功等自动通知的模板内容）❌ 待开发
- 邮件配置（设置 SMTP 邮箱参数与测试发送）✅ ThinkAdmin内置
- 通知记录（所有用户收到通知的状态与历史）❌ 待开发

### 🧠 系统设置模块
**thinkadmin 核心功能**：
- AI模型配置（配置 GPT-4、Claude、文心一言等的 API 接入）✅ 已实现
- 接口密钥管理（维护所有外部服务接口所需的 Token、Key 等）⚠️ 可集成到AI模型管理
- Webhook 配置（用于对接 n8n、查重平台等系统的回调地址）❌ 待开发
- 内容风控规则（定义敏感词、内容违规判断标准）❌ 待开发
- 基础参数设置（全局配置项，如最大字数、任务超时时间）⚠️ 可使用ThinkAdmin内置配置

**n8n 监控集成**：
- AI接口调用监控与日志
- 任务执行状态追踪
- 错误处理与重试机制


## 🎯 thinkadmin + n8n 分工详细说明

### 📄 thinkadmin 后台管理功能
```
📄 写作中心
├── 论文类型管理 ✅
├── 大纲模板管理 ✅
├── 提示词模板管理 ⚠️
├── 正文模板管理 ❌
├── 写作任务管理 ⚠️
└── 草稿箱管理 ❌

🔄 降重与查重
├── 降重记录管理 ❌
├── 降重模型配置 ❌
├── 查重记录管理 ❌
└── 查重接口配置 ❌

📁 文档导出
├── 导出样式模板 ❌
├── 下载记录管理 ❌
└── 导出任务监控 ❌

👤 用户中心
├── 用户列表 ✅ (ThinkAdmin内置)
├── VIP 套餐管理 ⚠️ (account插件)
└── 用户积分管理 ⚠️ (payment插件)

💰 收费系统
├── 订单管理 ⚠️ (payment插件)
├── 套餐配置 ⚠️ (payment插件)
├── 充值记录 ⚠️ (payment插件)
└── 发票管理 ❌

📬 通知与消息
├── 系统通知记录 ❌
├── 消息模板管理 ❌
├── 邮件配置 ✅ (ThinkAdmin内置)
└── 通知记录 ❌

🧠 系统设置
├── AI模型配置 ✅
├── 接口密钥管理 ⚠️
├── Webhook 配置 ❌
├── 内容风控规则 ❌
└── 基础参数设置 ⚠️ (ThinkAdmin内置)
```

### 🔄 n8n 工作流引擎功能
```
🚀 核心工作流
├── 论文写作流程
│   ├── 提纲生成工作流
│   ├── 分段写作工作流
│   ├── 全文合并工作流
│   └── 格式化处理工作流
├── 语义降重流程
│   ├── 文档解析工作流
│   ├── AI降重工作流
│   ├── 对比分析工作流
│   └── 重复率预估工作流
├── 查重处理流程
│   ├── 文档提交工作流
│   ├── 结果轮询工作流
│   ├── 报告解析工作流
│   └── 通知推送工作流
└── 文档导出流程
    ├── 格式转换工作流
    ├── 样式应用工作流
    └── 文件生成工作流

🔗 外部集成
├── AI模型集成（OpenAI、百度、阿里等）
├── 查重接口集成（PaperPass、维普、万方等）
├── 文件存储集成（本地/云存储）
├── 邮件服务集成
├── 支付回调处理
└── Webhook 通知

⚡ 任务管理
├── 异步任务队列
├── 任务状态追踪 ✅
├── 错误重试机制
├── 任务优先级控制
├── 负载均衡处理
└── 监控告警 ✅

� 数据流转
├── thinkadmin → n8n：任务创建、参数传递
├── n8n → thinkadmin：状态更新、结果回写
├── n8n → 外部API：服务调用、数据获取
└── 外部API → n8n：回调处理、状态同步
```

## 🔧 技术实现优势

### thinkadmin 优势
- **快速开发**：内置用户系统、权限管理、CRUD操作
- **界面友好**：现成的管理后台界面，支持响应式设计
- **菜单管理**：完善的菜单权限系统，支持角色分组和细粒度权限控制
- **插件生态**：丰富的插件系统（account、payment等），快速集成商业化功能
- **扩展性强**：基于ThinkPHP，易于二次开发
- **稳定可靠**：成熟的框架，社区支持完善

### n8n 优势
- **可视化编排**：拖拽式工作流设计，便于调试和维护
- **丰富集成**：内置200+服务连接器，支持自定义节点
- **异步处理**：天然支持长时间任务和复杂流程
- **错误处理**：完善的重试机制和错误处理
- **扩展灵活**：支持JavaScript代码节点，可实现复杂逻辑

### 架构优势
- **职责分离**：thinkadmin专注管理，n8n专注流程
- **松耦合**：通过API和Webhook通信，便于独立部署和扩展
- **高可用**：任一组件故障不影响整体系统运行
- **易维护**：业务逻辑可视化，便于排查问题和优化流程

## 🚀 部署与实施方案

### 环境要求
```
thinkadmin 环境：
├── PHP 7.4+ / 8.0+
├── MySQL 5.7+ / 8.0+
├── Nginx / Apache
├── Redis（缓存和会话）
└── Composer

n8n 环境：
├── Node.js 16+
├── PostgreSQL / MySQL（工作流数据）
├── Redis（队列和缓存）
└── Docker（推荐）
```

### 部署架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户前端      │    │   thinkadmin     │    │      n8n        │
│   (Web/Mobile)  │◄──►│   (管理后台)    │◄──►│   (工作流引擎)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────┐         ┌─────────────┐
                       │   MySQL     │         │ PostgreSQL  │
                       │ (业务数据)  │         │ (工作流数据)│
                       └─────────────┘         └─────────────┘
                              │                        │
                              └────────┬───────────────┘
                                       ▼
                                ┌─────────────┐
                                │    Redis    │
                                │ (缓存/队列) │
                                └─────────────┘
```

### 实施步骤

#### 第一阶段：基础环境搭建
1. **thinkadmin 部署**
   - 安装 thinkadmin 框架
   - 配置数据库和基础模块
   - 设置用户系统和权限管理
   - 配置支付系统集成

2. **n8n 部署**
   - Docker 部署 n8n 实例
   - 配置数据库连接
   - 设置基础认证和安全配置

#### 第二阶段：核心功能开发
1. **thinkadmin 定制开发**
   - 论文管理模块开发
   - 模板管理系统
   - 用户中心扩展
   - API 接口开发（与n8n通信）

2. **n8n 工作流设计**
   - 论文写作工作流
   - 语义降重工作流
   - 查重处理工作流
   - 文档导出工作流

#### 第三阶段：集成与测试
1. **系统集成**
   - thinkadmin 与 n8n API 对接
   - Webhook 配置和测试
   - 数据流转验证

2. **功能测试**
   - 单元测试
   - 集成测试
   - 性能测试
   - 用户体验测试

#### 第四阶段：上线与优化
1. **生产部署**
   - 服务器配置优化
   - 负载均衡设置
   - 监控和日志配置

2. **持续优化**
   - 性能监控
   - 用户反馈收集
   - 功能迭代优化

## 📋 开发清单

### 阶段一：完善写作中心模块
- [x] 论文类型管理 ✅ 已完成
- [x] 大纲模板管理 ✅ 已完成
- [x] AI模型管理 ✅ 已完成
- [ ] 提示词模板管理控制器开发
- [ ] 正文模板管理功能开发
- [ ] 写作任务管理功能完善（状态跟踪、重试机制）
- [ ] 草稿箱管理功能开发

### 阶段二：降重与查重模块开发
- [ ] 降重记录管理数据表设计
- [ ] 降重记录管理控制器开发
- [ ] 查重记录管理数据表设计
- [ ] 查重记录管理控制器开发
- [ ] 查重接口配置管理
- [ ] 降重模型配置（复用AI模型管理）

### 阶段三：文档导出模块开发
- [ ] 导出样式模板管理
- [ ] 下载记录管理
- [ ] 导出任务监控
- [ ] 文档格式转换集成

### 阶段四：商业化功能完善
- [ ] 用户中心集成（account插件）
- [ ] 支付系统集成（payment插件）
- [ ] VIP套餐管理
- [ ] 发票管理系统
- [ ] 通知消息系统

### 阶段五：系统设置完善
- [ ] Webhook配置管理
- [ ] 内容风控规则
- [ ] 接口密钥统一管理
- [ ] 系统参数配置优化

### n8n 工作流开发
- [ ] AI模型集成节点
- [ ] 论文写作工作流
- [ ] 语义降重工作流
- [ ] 查重处理工作流
- [ ] 文档处理工作流
- [ ] 通知推送工作流
- [ ] 错误处理和重试机制

### 集成与部署
- [ ] API 接口对接
- [ ] Webhook 配置
- [ ] 数据库设计和迁移
- [ ] 服务器环境配置
- [ ] 监控和日志系统
- [ ] 安全配置和测试

## 🎯 项目优势总结

通过 **thinkadmin + n8n** 架构，本项目实现了：

1. **快速开发**：利用 thinkadmin 成熟框架，减少基础功能开发时间
2. **灵活扩展**：n8n 可视化工作流，便于业务逻辑调整和新功能添加
3. **稳定可靠**：成熟技术栈，降低系统风险
4. **易于维护**：清晰的架构分层，便于团队协作和后期维护
5. **成本控制**：开源技术栈，降低授权成本

这种架构特别适合需要复杂业务流程编排的AI应用场景，既保证了系统的稳定性，又提供了足够的灵活性来应对业务需求的变化。

## 🎯 菜单配置与权限管理

### 菜单结构设计原则
- **按业务流程组织**：写作中心 → 降重查重 → 文档导出 → 用户管理 → 系统设置
- **权限分级管理**：支持角色权限绑定，可通过 ThinkAdmin 权限控制器实现分组管控
- **图标统一规范**：使用 Layui Icon 或自定义 Iconfont
- **路由标识规范**：每个菜单添加 `node` 节点值，例如 `admin/paper_type/index`

### 当前菜单实现状态
```php
// 已实现的菜单配置（database/migrations/20250102000001_install_paper_project.php）
PhinxExtend::write2menu([
    [
        'name' => '论文管理',
        'sort' => '200',
        'subs' => [
            ['name' => '论文项目管理', 'icon' => 'layui-icon layui-icon-file', 'node' => 'admin/paper_project/index'],
            ['name' => '论文类型管理', 'icon' => 'layui-icon layui-icon-template-1', 'node' => 'admin/paper_type/index'],
            ['name' => '大纲模板管理', 'icon' => 'layui-icon layui-icon-list', 'node' => 'admin/outline_template/index'],
            ['name' => 'AI模型管理', 'icon' => 'layui-icon layui-icon-engine', 'node' => 'admin/ai_model/index'],
        ],
    ],
]);
```

### 完整菜单配置规划
详见项目根目录 `menu.md` 文件，包含7个主要功能模块的完整菜单结构设计：
- 📄 写作中心（6个子菜单）
- 🔄 降重与查重（4个子菜单）
- 📁 文档导出（3个子菜单）
- 👤 用户中心（3个子菜单）
- 💰 收费系统（4个子菜单）
- 📬 通知与消息（4个子菜单）
- 🧠 系统设置（5个子菜单）
