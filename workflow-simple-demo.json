{"name": "AI论文写作演示流程", "nodes": [{"parameters": {"httpMethod": "POST", "path": "demo-paper", "responseMode": "responseNode", "options": {}}, "id": "demo-webhook", "name": "演示触发器", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "demo-paper-webhook"}, {"parameters": {"model": "gpt-4", "messages": {"messageType": "multipleMessages", "messages": [{"role": "system", "content": "你是一个专业的学术论文写作助手。请根据用户提供的主题生成一个完整的论文，包含标题、摘要、引言、主体内容、结论和参考文献。"}, {"role": "user", "content": "论文主题：{{ $json.topic }}\n关键词：{{ $json.keywords }}\n字数要求：{{ $json.word_count || '5000字' }}\n\n请生成完整的学术论文。"}]}, "options": {"temperature": 0.7, "maxTokens": 4000}}, "id": "ai-generate-paper", "name": "AI生成论文", "type": "n8n-nodes-base.openAi", "typeVersion": 1.3, "position": [460, 300], "credentials": {"openAiApi": {"id": "openai-demo-credentials", "name": "OpenAI Demo API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "enable-polish", "leftValue": "={{ $('demo-webhook').item.json.enable_polish }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "if-enable-polish", "name": "是否启用润色", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"model": "gpt-4", "messages": {"messageType": "multipleMessages", "messages": [{"role": "system", "content": "请对以下论文内容进行学术语言润色，提升表达的准确性和专业性，确保符合学术写作规范。"}, {"role": "user", "content": "{{ $('ai-generate-paper').item.json.choices[0].message.content }}"}]}, "options": {"temperature": 0.6, "maxTokens": 4000}}, "id": "ai-polish-paper", "name": "论文润色", "type": "n8n-nodes-base.openAi", "typeVersion": 1.3, "position": [900, 200], "credentials": {"openAiApi": {"id": "openai-demo-credentials", "name": "OpenAI Demo API"}}}, {"parameters": {"respondWith": "json", "responseBody": {"success": true, "message": "论文生成完成", "topic": "={{ $('demo-webhook').item.json.topic }}", "content": "={{ $('ai-generate-paper').item.json.choices[0].message.content }}", "polished_content": "={{ $('ai-polish-paper').item.json.choices[0].message.content || null }}", "word_count": "={{ $('ai-generate-paper').item.json.choices[0].message.content.length }}", "generated_at": "={{ new Date().toISOString() }}"}}, "id": "return-demo-result", "name": "返回演示结果", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 300]}], "connections": {"演示触发器": {"main": [[{"node": "AI生成论文", "type": "main", "index": 0}]]}, "AI生成论文": {"main": [[{"node": "是否启用润色", "type": "main", "index": 0}]]}, "是否启用润色": {"main": [[{"node": "论文润色", "type": "main", "index": 0}], [{"node": "返回演示结果", "type": "main", "index": 0}]]}, "论文润色": {"main": [[{"node": "返回演示结果", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "demo-paper-workflow", "tags": ["演示", "AI写作", "简化版"]}