# N8N 节点安装指南

## 🚨 **常见错误解决**

当您看到 "Install this node to use it" 错误时，说明工作流中使用了未安装的节点。

## 📦 **节点安装方法**

### 方法1：通过N8N界面安装（推荐）

1. **打开N8N管理界面**
   - 访问您的N8N实例（通常是 http://localhost:5678）
   - 登录到管理界面

2. **进入社区节点管理**
   - 点击左侧菜单 **"Settings"** (设置)
   - 选择 **"Community Nodes"** (社区节点)

3. **安装所需节点**
   ```
   搜索并安装以下节点包：
   - n8n-nodes-openai (OpenAI集成)
   - n8n-nodes-mysql (MySQL数据库)
   - n8n-nodes-aws (AWS服务)
   - n8n-nodes-microsoft-office (Office文档)
   ```

### 方法2：通过npm命令安装

如果您有服务器访问权限：

```bash
# 全局安装N8N和相关节点
npm install -g n8n
npm install -g n8n-nodes-openai
npm install -g n8n-nodes-mysql
npm install -g n8n-nodes-aws

# 或者在N8N项目目录中安装
cd /path/to/n8n
npm install n8n-nodes-openai
npm install n8n-nodes-mysql
npm install n8n-nodes-aws
```

### 方法3：Docker环境安装

如果使用Docker运行N8N：

```dockerfile
# 在Dockerfile中添加
FROM n8nio/n8n:latest
USER root
RUN npm install -g n8n-nodes-openai n8n-nodes-mysql n8n-nodes-aws
USER node
```

## 🔧 **推荐的节点配置**

### 必需的标准节点（N8N内置）
- ✅ `n8n-nodes-base.webhook` - Webhook触发器
- ✅ `n8n-nodes-base.httpRequest` - HTTP请求
- ✅ `n8n-nodes-base.mysql` - MySQL数据库
- ✅ `n8n-nodes-base.if` - 条件判断
- ✅ `n8n-nodes-base.respondToWebhook` - 响应返回
- ✅ `n8n-nodes-base.writeFile` - 文件写入

### 可选的社区节点
- 🔌 `n8n-nodes-openai` - OpenAI API集成
- 🔌 `n8n-nodes-aws` - AWS服务集成
- 🔌 `n8n-nodes-microsoft-office` - Office文档处理

## 📋 **解决方案选择**

### 选项1：使用标准节点版本（推荐）
使用 `workflow-standard-nodes.json` 配置文件，它只使用N8N内置的标准节点：
- ✅ 无需安装额外节点
- ✅ 兼容性最好
- ✅ 部署简单

### 选项2：安装社区节点
如果需要完整功能，安装所需的社区节点：
- 🔌 功能更丰富
- 🔌 需要额外配置
- 🔌 可能有兼容性问题

## 🛠️ **配置文件对比**

| 配置文件 | 节点类型 | 功能完整度 | 部署难度 |
|---------|---------|-----------|----------|
| `workflow-standard-nodes.json` | 标准节点 | 基础功能 | 简单 |
| `workflow-outline-generation.json` | 混合节点 | 完整功能 | 中等 |
| `workflow-content-generation.json` | 混合节点 | 完整功能 | 复杂 |
| `workflow-simple-demo.json` | 标准节点 | 演示功能 | 简单 |

## 🚀 **快速开始建议**

1. **第一步：使用标准节点版本**
   - 导入 `workflow-standard-nodes.json`
   - 配置基本的API凭据（OpenAI、MySQL）
   - 测试基本功能

2. **第二步：根据需要扩展**
   - 如果需要更多功能，再安装社区节点
   - 逐步迁移到完整版配置

3. **第三步：生产部署**
   - 确保所有节点正常工作
   - 配置监控和日志

## 🔑 **凭据配置**

无论使用哪种节点，都需要配置以下凭据：

### OpenAI API（必需）
```json
{
  "name": "openai-api-key",
  "type": "openAiApi",
  "data": {
    "apiKey": "your-openai-api-key"
  }
}
```

### MySQL数据库（必需）
```json
{
  "name": "mysql-db",
  "type": "mysql",
  "data": {
    "host": "localhost",
    "port": 3306,
    "database": "your_database",
    "username": "your_username",
    "password": "your_password"
  }
}
```

## 📞 **故障排除**

### 问题1：节点无法安装
**解决方案：**
- 检查网络连接
- 确认npm权限
- 尝试使用标准节点版本

### 问题2：API调用失败
**解决方案：**
- 检查API密钥配置
- 确认API配额和限制
- 查看N8N执行日志

### 问题3：数据库连接失败
**解决方案：**
- 检查数据库连接参数
- 确认数据库服务运行状态
- 验证用户权限

## 💡 **最佳实践**

1. **从简单开始**：先使用标准节点版本测试
2. **逐步扩展**：根据实际需要添加功能
3. **备份配置**：保存工作流配置文件
4. **监控日志**：定期检查执行日志
5. **版本管理**：记录节点版本和配置变更

## 📚 **相关资源**

- [N8N官方文档](https://docs.n8n.io/)
- [N8N社区节点](https://n8n.io/integrations/)
- [OpenAI API文档](https://platform.openai.com/docs/)
- [MySQL连接配置](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mysql/)
