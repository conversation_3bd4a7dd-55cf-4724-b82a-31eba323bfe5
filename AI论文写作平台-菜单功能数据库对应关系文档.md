# AI论文写作平台 - 菜单功能与数据库表对应关系文档

## 项目概述

基于 **ThinkAdmin + n8n** 架构的智能论文写作平台，提供AIGC语义降重和第三方论文查重集成服务。本文档详细梳理了各个菜单功能与数据库表的对应关系，以及字段关联说明。

---

## 📄 写作中心模块

### 1. 论文类型管理
**菜单路径**: 写作中心 → 论文类型管理  
**主要数据表**: `bxw_paper_type`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| name | 类型名称 | VARCHAR(100) | 文本框 | 如：毕业论文、期刊论文 |
| description | 描述 | TEXT | 文本域 | 类型详细说明 |
| word_count_min | 最小字数 | INTEGER | 数字框 | 默认6000 |
| word_count_max | 最大字数 | INTEGER | 数字框 | 默认30000 |
| outline_template_id | 大纲模板ID | INTEGER | 下拉选择 | 关联bxw_outline_template.id |
| prompt_template_id | 提示词模板ID | INTEGER | 下拉选择 | 关联bxw_prompt_template.id |
| sort | 排序 | INTEGER | 数字框 | 显示顺序 |
| status | 状态 | INTEGER | 开关 | 1启用/0禁用 |

#### 字段关联关系
- `outline_template_id` → `bxw_outline_template.id` (一对一，可选)
- `prompt_template_id` → `bxw_prompt_template.id` (一对一，可选)

### 2. 大纲模板管理
**菜单路径**: 写作中心 → 大纲模板管理  
**主要数据表**: `bxw_outline_template`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| name | 模板名称 | VARCHAR(100) | 文本框 | 模板标识名称 |
| paper_type_id | 论文类型 | INTEGER | 下拉选择 | 关联bxw_paper_type.id |
| template_content | 模板内容 | TEXT | 富文本编辑器 | JSON格式的章节结构 |
| description | 描述 | TEXT | 文本域 | 模板说明 |
| is_default | 是否默认 | INTEGER | 开关 | 1默认/0非默认 |
| usage_count | 使用次数 | INTEGER | 只读 | 统计字段 |
| status | 状态 | INTEGER | 开关 | 1启用/0禁用 |

#### 字段关联关系
- `paper_type_id` → `bxw_paper_type.id` (多对一，可选)

### 3. 提示词模板管理
**菜单路径**: 写作中心 → 提示词模板管理  
**主要数据表**: `bxw_prompt_template`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| name | 模板名称 | VARCHAR(100) | 文本框 | 提示词模板名称 |
| type | 模板类型 | VARCHAR(50) | 下拉选择 | outline/writing/rewrite等 |
| paper_type_id | 论文类型 | INTEGER | 下拉选择 | 关联bxw_paper_type.id |
| prompt_content | 提示词内容 | TEXT | 文本域 | AI提示词模板 |
| variables | 变量配置 | TEXT | JSON编辑器 | 模板变量定义 |
| ai_model | 适用模型 | VARCHAR(50) | 下拉选择 | 关联bxw_ai_model.model_code |
| description | 描述 | TEXT | 文本域 | 模板说明 |
| is_default | 是否默认 | INTEGER | 开关 | 1默认/0非默认 |
| usage_count | 使用次数 | INTEGER | 只读 | 统计字段 |
| status | 状态 | INTEGER | 开关 | 1启用/0禁用 |

#### 字段关联关系
- `paper_type_id` → `bxw_paper_type.id` (多对一，可选)
- `ai_model` → `bxw_ai_model.model_code` (多对一，可选)

### 4. 写作任务管理
**菜单路径**: 写作中心 → 写作任务管理  
**主要数据表**: `bxw_paper_project`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| title | 论文标题 | VARCHAR(255) | 文本框 | 论文题目 |
| paper_type_id | 论文类型 | INTEGER | 下拉选择 | 关联bxw_paper_type.id |
| subject | 学科领域 | VARCHAR(100) | 文本框 | 专业领域 |
| keywords | 关键词 | VARCHAR(500) | 标签输入 | 逗号分隔 |
| requirements | 具体要求 | TEXT | 文本域 | 用户需求描述 |
| target_word_count | 目标字数 | INTEGER | 数字框 | 期望字数 |
| writing_style | 写作风格 | VARCHAR(50) | 下拉选择 | academic/formal/casual |
| outline_content | 大纲内容 | TEXT | 只读 | JSON格式大纲 |
| outline_version | 大纲版本 | INTEGER | 只读 | 版本号 |
| content | 正文内容 | TEXT | 只读 | 生成的论文内容 |
| current_word_count | 当前字数 | INTEGER | 只读 | 实际字数统计 |
| status | 任务状态 | VARCHAR(20) | 状态标签 | draft/writing/completed等 |
| is_draft | 是否草稿 | INTEGER | 开关 | 1草稿/0正式 |
| draft_version | 草稿版本 | INTEGER | 只读 | 草稿版本号 |
| parent_id | 父项目ID | INTEGER | 隐藏 | 关联bxw_paper_project.id |
| n8n_workflow_id | 工作流ID | VARCHAR(100) | 只读 | n8n工作流标识 |
| n8n_execution_id | 执行ID | VARCHAR(100) | 只读 | n8n执行标识 |
| progress | 进度 | INTEGER | 进度条 | 0-100百分比 |
| error_message | 错误信息 | TEXT | 只读 | 失败原因 |

#### 字段关联关系
- `user_id` → `bxw_user.id` (多对一，必填)
- `paper_type_id` → `bxw_paper_type.id` (多对一，必填)
- `parent_id` → `bxw_paper_project.id` (一对一，可选，用于草稿关联)

### 5. 草稿箱管理
**菜单路径**: 写作中心 → 草稿箱管理  
**主要数据表**: `bxw_paper_project` (where is_draft=1)

#### 显示字段与表单
与写作任务管理相同，但筛选条件为 `is_draft=1`，主要展示：
- 草稿标题、类型、版本号
- 创建时间、更新时间
- 字数统计、完成进度
- 操作：编辑、发布、删除、版本对比

---

## 🔄 降重与查重模块

### 1. 降重记录管理
**菜单路径**: 降重与查重 → 降重记录管理  
**主要数据表**: `bxw_rewrite_task`, `bxw_rewrite_result`

#### 主表字段 (bxw_rewrite_task)
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| title | 任务标题 | VARCHAR(255) | 文本框 | 降重任务名称 |
| original_text | 原文内容 | TEXT | 文本域 | 待降重文本 |
| original_word_count | 原文字数 | INTEGER | 只读 | 字数统计 |
| rewrite_mode | 降重模式 | VARCHAR(50) | 下拉选择 | standard/deep/light |
| target_similarity | 目标相似度 | REAL | 数字框 | 期望重复率% |
| ai_model_id | AI模型 | INTEGER | 下拉选择 | 关联bxw_ai_model.id |
| status | 任务状态 | VARCHAR(20) | 状态标签 | pending/processing/completed |
| progress | 进度 | INTEGER | 进度条 | 0-100百分比 |
| error_message | 错误信息 | TEXT | 只读 | 失败原因 |

#### 结果表字段 (bxw_rewrite_result)
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| task_id | 任务ID | INTEGER | 隐藏 | 关联bxw_rewrite_task.id |
| ai_model_id | AI模型 | INTEGER | 只读 | 关联bxw_ai_model.id |
| rewritten_text | 降重结果 | TEXT | 文本域 | 改写后文本 |
| rewritten_word_count | 结果字数 | INTEGER | 只读 | 字数统计 |
| similarity_score | 相似度评分 | REAL | 只读 | 预估重复率 |
| quality_score | 质量评分 | REAL | 只读 | 改写质量 |
| processing_time | 处理时间 | INTEGER | 只读 | 毫秒 |
| is_selected | 是否选中 | INTEGER | 开关 | 用户选择的结果 |

#### 字段关联关系
- `user_id` → `bxw_user.id` (多对一，必填)
- `ai_model_id` → `bxw_ai_model.id` (多对一，可选)
- `task_id` → `bxw_rewrite_task.id` (多对一，必填)

### 2. 查重记录管理
**菜单路径**: 降重与查重 → 查重记录管理  
**主要数据表**: `bxw_check_task`, `bxw_check_detail`

#### 主表字段 (bxw_check_task)
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| title | 任务标题 | VARCHAR(255) | 文本框 | 查重任务名称 |
| file_path | 文件路径 | VARCHAR(255) | 文件上传 | 上传文件路径 |
| file_name | 文件名称 | VARCHAR(255) | 只读 | 原始文件名 |
| file_size | 文件大小 | INTEGER | 只读 | 字节数 |
| word_count | 文档字数 | INTEGER | 只读 | 字数统计 |
| check_api_id | 查重接口 | INTEGER | 下拉选择 | 关联bxw_check_api.id |
| external_task_id | 外部任务ID | VARCHAR(100) | 只读 | 第三方平台任务ID |
| status | 任务状态 | VARCHAR(20) | 状态标签 | pending/checking/completed |
| similarity_rate | 重复率 | REAL | 只读 | 查重结果百分比 |
| report_url | 报告链接 | VARCHAR(255) | 链接 | 在线查看地址 |
| report_path | 报告文件 | VARCHAR(255) | 文件下载 | 本地报告路径 |
| cost | 查重费用 | REAL | 只读 | 消耗金额 |
| error_message | 错误信息 | TEXT | 只读 | 失败原因 |

#### 字段关联关系
- `user_id` → `bxw_user.id` (多对一，必填)
- `check_api_id` → `bxw_check_api.id` (多对一，必填)

### 3. 查重接口配置
**菜单路径**: 降重与查重 → 查重接口配置  
**主要数据表**: `bxw_check_api`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| name | 接口名称 | VARCHAR(100) | 文本框 | 如：维普、知网 |
| provider | 服务商 | VARCHAR(50) | 文本框 | 服务商标识 |
| api_endpoint | 接口地址 | VARCHAR(255) | 文本框 | API端点URL |
| api_key | API密钥 | VARCHAR(255) | 密码框 | 接口密钥 |
| api_secret | API密钥 | VARCHAR(255) | 密码框 | 接口密钥 |
| cost_per_check | 单次费用 | REAL | 数字框 | 每次查重成本 |
| max_word_count | 最大字数 | INTEGER | 数字框 | 支持的最大字数 |
| supported_formats | 支持格式 | VARCHAR(255) | 标签输入 | 如：txt,doc,docx,pdf |
| priority | 优先级 | INTEGER | 数字框 | 使用优先级 |
| status | 状态 | INTEGER | 开关 | 1启用/0禁用 |
| health_status | 健康状态 | INTEGER | 状态标签 | 1正常/0异常 |
| last_health_check | 最后检查 | DATETIME | 只读 | 健康检查时间 |

---

## 📁 文档导出模块

### 1. 导出样式模板
**菜单路径**: 文档导出 → 导出样式模板  
**主要数据表**: `bxw_document_template`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| name | 模板名称 | VARCHAR(100) | 文本框 | 模板标识名称 |
| type | 模板类型 | VARCHAR(50) | 下拉选择 | docx/pdf/html |
| paper_type_id | 论文类型 | INTEGER | 下拉选择 | 关联bxw_paper_type.id |
| template_content | 模板内容 | TEXT | 文件上传 | 模板文件内容 |
| style_config | 样式配置 | TEXT | JSON编辑器 | 字体、间距等配置 |
| is_default | 是否默认 | INTEGER | 开关 | 1默认/0非默认 |
| usage_count | 使用次数 | INTEGER | 只读 | 统计字段 |
| status | 状态 | INTEGER | 开关 | 1启用/0禁用 |

#### 字段关联关系
- `paper_type_id` → `bxw_paper_type.id` (多对一，可选)

### 2. 下载记录管理
**菜单路径**: 文档导出 → 下载记录管理  
**主要数据表**: `bxw_export_record`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| source_type | 来源类型 | VARCHAR(50) | 下拉选择 | paper/rewrite/check |
| source_id | 来源ID | INTEGER | 数字框 | 关联对应表的ID |
| export_format | 导出格式 | VARCHAR(20) | 下拉选择 | docx/pdf/html |
| template_id | 模板ID | INTEGER | 下拉选择 | 关联bxw_document_template.id |
| file_name | 文件名称 | VARCHAR(255) | 只读 | 生成的文件名 |
| file_path | 文件路径 | VARCHAR(255) | 文件下载 | 文件存储路径 |
| file_size | 文件大小 | INTEGER | 只读 | 字节数 |
| download_count | 下载次数 | INTEGER | 只读 | 统计字段 |
| status | 导出状态 | VARCHAR(20) | 状态标签 | pending/processing/completed |
| error_message | 错误信息 | TEXT | 只读 | 失败原因 |

#### 字段关联关系
- `user_id` → `bxw_user.id` (多对一，必填)
- `template_id` → `bxw_document_template.id` (多对一，可选)
- `source_id` → 根据source_type关联不同表的ID

---

## 👤 用户中心模块

### 1. 用户列表
**菜单路径**: 用户中心 → 用户列表
**主要数据表**: `bxw_user`, `bxw_user_quota`

#### 显示字段与表单 (bxw_user)
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| username | 用户名 | VARCHAR(50) | 文本框 | 登录用户名 |
| email | 邮箱 | VARCHAR(100) | 邮箱框 | 登录邮箱 |
| phone | 手机号 | VARCHAR(20) | 手机框 | 联系电话 |
| nickname | 昵称 | VARCHAR(50) | 文本框 | 显示昵称 |
| avatar | 头像 | VARCHAR(255) | 图片上传 | 头像文件路径 |
| status | 状态 | INTEGER | 开关 | 1正常/0禁用 |
| user_type | 用户类型 | INTEGER | 下拉选择 | 1普通/2VIP/3企业 |
| credits | 积分余额 | INTEGER | 数字框 | 当前积分 |
| vip_expire_time | VIP到期时间 | DATETIME | 日期选择器 | VIP会员到期 |
| last_login_time | 最后登录 | DATETIME | 只读 | 最后登录时间 |
| last_login_ip | 登录IP | VARCHAR(50) | 只读 | 最后登录IP |

#### 配额表字段 (bxw_user_quota)
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| user_id | 用户ID | INTEGER | 隐藏 | 关联bxw_user.id |
| quota_type | 配额类型 | VARCHAR(50) | 下拉选择 | writing/rewrite/check |
| daily_limit | 日限额 | INTEGER | 数字框 | 每日使用限制 |
| monthly_limit | 月限额 | INTEGER | 数字框 | 每月使用限制 |
| daily_used | 日已用 | INTEGER | 只读 | 今日已使用 |
| monthly_used | 月已用 | INTEGER | 只读 | 本月已使用 |
| last_reset_date | 最后重置日期 | DATE | 只读 | 配额重置日期 |

#### 字段关联关系
- `bxw_user_quota.user_id` → `bxw_user.id` (一对多)

### 2. VIP套餐管理
**菜单路径**: 用户中心 → VIP套餐管理
**主要数据表**: `bxw_package`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| name | 套餐名称 | VARCHAR(100) | 文本框 | 套餐标识名称 |
| type | 套餐类型 | VARCHAR(50) | 下拉选择 | credits/vip/combo |
| description | 套餐描述 | TEXT | 文本域 | 套餐详细说明 |
| credits | 赠送积分 | INTEGER | 数字框 | 包含积分数量 |
| vip_days | VIP天数 | INTEGER | 数字框 | VIP会员天数 |
| writing_quota | 写作配额 | INTEGER | 数字框 | 写作次数限制 |
| rewrite_quota | 降重配额 | INTEGER | 数字框 | 降重次数限制 |
| check_quota | 查重配额 | INTEGER | 数字框 | 查重次数限制 |
| original_price | 原价 | REAL | 数字框 | 套餐原价 |
| sale_price | 售价 | REAL | 数字框 | 实际售价 |
| discount_rate | 折扣率 | REAL | 数字框 | 折扣百分比 |
| sort | 排序 | INTEGER | 数字框 | 显示顺序 |
| is_hot | 是否热门 | INTEGER | 开关 | 1热门/0普通 |
| status | 状态 | INTEGER | 开关 | 1启用/0禁用 |

### 3. 用户积分管理
**菜单路径**: 用户中心 → 用户积分管理
**主要数据表**: `bxw_credits_log`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| type | 变动类型 | VARCHAR(50) | 下拉选择 | recharge/consume/refund |
| amount | 变动数量 | INTEGER | 数字框 | 正数增加/负数减少 |
| balance_before | 变动前余额 | INTEGER | 只读 | 操作前积分 |
| balance_after | 变动后余额 | INTEGER | 只读 | 操作后积分 |
| related_id | 关联ID | INTEGER | 数字框 | 关联业务ID |
| related_type | 关联类型 | VARCHAR(50) | 下拉选择 | order/task/refund |
| description | 变动说明 | VARCHAR(255) | 文本框 | 变动原因描述 |
| createtime | 创建时间 | DATETIME | 只读 | 记录时间 |

#### 字段关联关系
- `user_id` → `bxw_user.id` (多对一，必填)
- `related_id` → 根据related_type关联不同表的ID

---

## 💰 收费系统模块

### 1. 订单管理
**菜单路径**: 收费系统 → 订单管理
**主要数据表**: `bxw_order`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| order_no | 订单号 | VARCHAR(32) | 只读 | 唯一订单编号 |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| package_id | 套餐ID | INTEGER | 下拉选择 | 关联bxw_package.id |
| package_name | 套餐名称 | VARCHAR(100) | 只读 | 套餐快照名称 |
| original_price | 原价 | REAL | 数字框 | 套餐原价 |
| discount_amount | 优惠金额 | REAL | 数字框 | 折扣金额 |
| final_price | 实付金额 | REAL | 数字框 | 最终支付金额 |
| payment_method | 支付方式 | VARCHAR(50) | 下拉选择 | wechat/alipay/bank |
| payment_status | 支付状态 | VARCHAR(20) | 状态标签 | pending/paid/failed |
| transaction_id | 交易号 | VARCHAR(100) | 只读 | 第三方交易号 |
| paid_time | 支付时间 | DATETIME | 只读 | 实际支付时间 |
| expired_time | 过期时间 | DATETIME | 日期选择器 | 订单过期时间 |
| coupon_id | 优惠券ID | INTEGER | 下拉选择 | 关联bxw_coupon.id |
| remark | 备注 | VARCHAR(500) | 文本域 | 订单备注信息 |

#### 字段关联关系
- `user_id` → `bxw_user.id` (多对一，必填)
- `package_id` → `bxw_package.id` (多对一，必填)
- `coupon_id` → `bxw_coupon.id` (多对一，可选)

### 2. 优惠券管理
**菜单路径**: 收费系统 → 优惠券管理
**主要数据表**: `bxw_coupon`, `bxw_user_coupon`

#### 优惠券表字段 (bxw_coupon)
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| name | 优惠券名称 | VARCHAR(100) | 文本框 | 优惠券标题 |
| code | 优惠码 | VARCHAR(50) | 文本框 | 兑换码 |
| type | 优惠类型 | VARCHAR(20) | 下拉选择 | percent/fixed |
| value | 优惠值 | REAL | 数字框 | 折扣值或固定金额 |
| min_amount | 最小金额 | REAL | 数字框 | 使用门槛 |
| max_discount | 最大优惠 | REAL | 数字框 | 最大优惠金额 |
| total_quantity | 总数量 | INTEGER | 数字框 | 发行总量 |
| used_quantity | 已使用数量 | INTEGER | 只读 | 已使用数量 |
| per_user_limit | 每人限制 | INTEGER | 数字框 | 每用户使用次数 |
| start_time | 开始时间 | DATETIME | 日期选择器 | 有效期开始 |
| end_time | 结束时间 | DATETIME | 日期选择器 | 有效期结束 |
| applicable_packages | 适用套餐 | VARCHAR(500) | 多选框 | 可用套餐ID列表 |
| status | 状态 | INTEGER | 开关 | 1启用/0禁用 |

### 3. 发票管理
**菜单路径**: 收费系统 → 发票管理
**主要数据表**: `bxw_invoice`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| order_id | 订单ID | INTEGER | 订单选择器 | 关联bxw_order.id |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| invoice_type | 发票类型 | VARCHAR(20) | 下拉选择 | personal/company |
| invoice_title | 发票抬头 | VARCHAR(200) | 文本框 | 开票抬头 |
| tax_number | 税号 | VARCHAR(50) | 文本框 | 纳税人识别号 |
| invoice_content | 发票内容 | VARCHAR(500) | 文本框 | 开票内容 |
| invoice_amount | 发票金额 | REAL | 数字框 | 开票金额 |
| status | 开票状态 | VARCHAR(20) | 状态标签 | pending/issued/failed |
| invoice_code | 发票代码 | VARCHAR(50) | 只读 | 发票代码 |
| invoice_number | 发票号码 | VARCHAR(50) | 只读 | 发票号码 |
| invoice_url | 发票链接 | VARCHAR(255) | 链接 | 电子发票链接 |
| invoice_file_path | 发票文件 | VARCHAR(255) | 文件下载 | 发票文件路径 |
| apply_time | 申请时间 | DATETIME | 只读 | 申请时间 |
| issue_time | 开具时间 | DATETIME | 只读 | 实际开具时间 |
| remark | 备注 | VARCHAR(500) | 文本域 | 开票备注 |

#### 字段关联关系
- `order_id` → `bxw_order.id` (一对一，必填)
- `user_id` → `bxw_user.id` (多对一，必填)

---

## 📬 通知与消息模块

### 1. 消息模板管理
**菜单路径**: 通知与消息 → 消息模板管理
**主要数据表**: `bxw_message_template`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| code | 模板代码 | VARCHAR(50) | 文本框 | 唯一标识码 |
| name | 模板名称 | VARCHAR(100) | 文本框 | 模板显示名称 |
| type | 消息类型 | VARCHAR(20) | 下拉选择 | system/email/sms |
| subject | 消息主题 | VARCHAR(255) | 文本框 | 邮件主题(可选) |
| content | 消息内容 | TEXT | 富文本编辑器 | 模板内容 |
| variables | 变量配置 | TEXT | JSON编辑器 | 模板变量定义 |
| status | 状态 | INTEGER | 开关 | 1启用/0禁用 |

### 2. 用户通知记录
**菜单路径**: 通知与消息 → 用户通知记录
**主要数据表**: `bxw_user_notification`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| type | 通知类型 | VARCHAR(50) | 下拉选择 | task/payment/system |
| title | 通知标题 | VARCHAR(255) | 文本框 | 通知标题 |
| content | 通知内容 | TEXT | 文本域 | 通知详细内容 |
| related_id | 关联ID | INTEGER | 数字框 | 关联业务ID |
| related_type | 关联类型 | VARCHAR(50) | 下拉选择 | task/order/system |
| is_read | 是否已读 | INTEGER | 开关 | 1已读/0未读 |
| read_time | 阅读时间 | DATETIME | 只读 | 用户阅读时间 |
| createtime | 创建时间 | DATETIME | 只读 | 通知创建时间 |

#### 字段关联关系
- `user_id` → `bxw_user.id` (多对一，必填)
- `related_id` → 根据related_type关联不同表的ID

### 3. 邮件发送记录
**菜单路径**: 通知与消息 → 邮件发送记录
**主要数据表**: `bxw_email_log`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| template_id | 模板ID | INTEGER | 下拉选择 | 关联bxw_message_template.id |
| to_email | 收件邮箱 | VARCHAR(255) | 邮箱框 | 接收邮箱地址 |
| subject | 邮件主题 | VARCHAR(255) | 文本框 | 邮件标题 |
| content | 邮件内容 | TEXT | 文本域 | 邮件正文 |
| status | 发送状态 | VARCHAR(20) | 状态标签 | pending/sent/failed |
| error_message | 错误信息 | TEXT | 只读 | 发送失败原因 |
| sent_time | 发送时间 | DATETIME | 只读 | 实际发送时间 |
| createtime | 创建时间 | DATETIME | 只读 | 记录创建时间 |

#### 字段关联关系
- `user_id` → `bxw_user.id` (多对一，可选)
- `template_id` → `bxw_message_template.id` (多对一，可选)

---

## 🧠 系统设置模块

### 1. AI模型配置
**菜单路径**: 系统设置 → AI模型配置
**主要数据表**: `bxw_ai_model`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| name | 模型名称 | VARCHAR(100) | 文本框 | 如：GPT-4 |
| provider | 服务商 | VARCHAR(50) | 下拉选择 | openai/baidu/aliyun |
| model_code | 模型代码 | VARCHAR(100) | 文本框 | 如：gpt-4 |
| api_endpoint | API端点 | VARCHAR(255) | 文本框 | 接口地址 |
| api_key | API密钥 | VARCHAR(255) | 密码框 | 接口密钥 |
| max_tokens | 最大令牌 | INTEGER | 数字框 | 单次请求最大令牌数 |
| temperature | 温度参数 | REAL | 数字框 | 0.0-2.0 |
| top_p | Top P | REAL | 数字框 | 0.0-1.0 |
| frequency_penalty | 频率惩罚 | REAL | 数字框 | -2.0-2.0 |
| presence_penalty | 存在惩罚 | REAL | 数字框 | -2.0-2.0 |
| cost_per_1k_tokens | 千令牌成本 | REAL | 数字框 | 美元计价 |
| priority | 优先级 | INTEGER | 数字框 | 使用优先级 |
| rate_limit_per_minute | 每分钟限制 | INTEGER | 数字框 | 请求频率限制 |
| status | 状态 | INTEGER | 开关 | 1启用/0禁用 |
| health_status | 健康状态 | INTEGER | 状态标签 | 1正常/0异常 |
| last_health_check | 最后检查 | DATETIME | 只读 | 健康检查时间 |

### 2. 系统配置管理
**菜单路径**: 系统设置 → 系统配置管理
**主要数据表**: `bxw_config`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| group | 配置分组 | VARCHAR(50) | 下拉选择 | system/ai/payment等 |
| name | 配置名称 | VARCHAR(100) | 文本框 | 配置项标识 |
| title | 显示标题 | VARCHAR(100) | 文本框 | 配置项显示名 |
| value | 配置值 | TEXT | 动态表单 | 根据type显示不同控件 |
| type | 值类型 | VARCHAR(20) | 下拉选择 | string/number/boolean |
| options | 选项配置 | TEXT | JSON编辑器 | 下拉选项等配置 |
| description | 配置说明 | VARCHAR(500) | 文本域 | 配置项说明 |
| sort | 排序 | INTEGER | 数字框 | 显示顺序 |

### 3. 内容风控规则
**菜单路径**: 系统设置 → 内容风控规则
**主要数据表**: `bxw_content_filter`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| name | 规则名称 | VARCHAR(100) | 文本框 | 规则标识名称 |
| type | 规则类型 | VARCHAR(50) | 下拉选择 | keyword/regex/ai |
| pattern | 匹配模式 | TEXT | 文本域 | 关键词或正则表达式 |
| action | 处理动作 | VARCHAR(20) | 下拉选择 | block/replace/warn |
| replacement | 替换内容 | VARCHAR(500) | 文本框 | 替换文本(可选) |
| severity | 严重程度 | VARCHAR(20) | 下拉选择 | low/medium/high |
| status | 状态 | INTEGER | 开关 | 1启用/0禁用 |

### 4. n8n工作流配置
**菜单路径**: 系统设置 → n8n工作流配置
**主要数据表**: `bxw_n8n_workflow`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| name | 工作流名称 | VARCHAR(100) | 文本框 | 工作流显示名称 |
| type | 工作流类型 | VARCHAR(50) | 下拉选择 | outline/writing/rewrite/check/export |
| n8n_workflow_id | n8n工作流ID | VARCHAR(100) | 文本框 | n8n平台的工作流ID |
| webhook_url | Webhook地址 | VARCHAR(255) | 文本框 | 触发工作流的URL |
| description | 描述 | TEXT | 文本域 | 工作流功能说明 |
| config | 配置参数 | TEXT | JSON编辑器 | 工作流配置参数 |
| is_active | 是否激活 | INTEGER | 开关 | 1激活/0未激活 |
| status | 状态 | INTEGER | 开关 | 1启用/0禁用 |

### 5. n8n执行记录
**菜单路径**: 系统设置 → n8n执行记录
**主要数据表**: `bxw_n8n_execution`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| workflow_id | 工作流ID | INTEGER | 下拉选择 | 关联bxw_n8n_workflow.id |
| n8n_execution_id | n8n执行ID | VARCHAR(100) | 只读 | n8n平台的执行ID |
| task_type | 任务类型 | VARCHAR(50) | 下拉选择 | outline/writing/rewrite等 |
| task_id | 任务ID | INTEGER | 数字框 | 关联具体任务表的ID |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| input_data | 输入数据 | TEXT | JSON查看器 | 工作流输入参数 |
| output_data | 输出数据 | TEXT | JSON查看器 | 工作流输出结果 |
| status | 执行状态 | VARCHAR(20) | 状态标签 | running/success/failed |
| start_time | 开始时间 | DATETIME | 只读 | 执行开始时间 |
| end_time | 结束时间 | DATETIME | 只读 | 执行结束时间 |
| duration | 执行时长 | INTEGER | 只读 | 执行时长(秒) |
| error_message | 错误信息 | TEXT | 只读 | 执行失败原因 |

#### 字段关联关系
- `workflow_id` → `bxw_n8n_workflow.id` (多对一，必填)
- `user_id` → `bxw_user.id` (多对一，必填)
- `task_id` → 根据task_type关联不同任务表的ID

---

## 📊 日志统计模块

### 1. 操作日志
**菜单路径**: 系统管理 → 操作日志
**主要数据表**: `bxw_operation_log`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| admin_id | 管理员ID | INTEGER | 管理员选择器 | 后台管理员ID |
| module | 操作模块 | VARCHAR(50) | 下拉选择 | paper/user/order等 |
| action | 操作动作 | VARCHAR(50) | 下拉选择 | create/update/delete等 |
| description | 操作描述 | VARCHAR(500) | 文本域 | 操作详细说明 |
| request_data | 请求数据 | TEXT | JSON查看器 | 请求参数 |
| response_data | 响应数据 | TEXT | JSON查看器 | 响应结果 |
| ip_address | IP地址 | VARCHAR(50) | 只读 | 操作者IP |
| user_agent | 用户代理 | VARCHAR(500) | 只读 | 浏览器信息 |
| execution_time | 执行时间 | INTEGER | 只读 | 执行耗时(毫秒) |
| createtime | 创建时间 | DATETIME | 只读 | 操作时间 |

#### 字段关联关系
- `user_id` → `bxw_user.id` (多对一，可选)
- `admin_id` → 管理员表ID (多对一，可选)

### 2. 错误日志
**菜单路径**: 系统管理 → 错误日志
**主要数据表**: `bxw_error_log`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| level | 错误级别 | VARCHAR(20) | 状态标签 | error/warning/info |
| category | 错误分类 | VARCHAR(50) | 下拉选择 | api/database/system等 |
| message | 错误消息 | VARCHAR(1000) | 文本域 | 错误描述 |
| context | 错误上下文 | TEXT | JSON查看器 | 错误发生时的上下文 |
| stack_trace | 堆栈跟踪 | TEXT | 文本域 | 错误堆栈信息 |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| request_id | 请求ID | VARCHAR(100) | 只读 | 请求唯一标识 |
| ip_address | IP地址 | VARCHAR(50) | 只读 | 用户IP |
| user_agent | 用户代理 | VARCHAR(500) | 只读 | 浏览器信息 |
| createtime | 创建时间 | DATETIME | 只读 | 错误发生时间 |

#### 字段关联关系
- `user_id` → `bxw_user.id` (多对一，可选)

### 3. 用户行为统计
**菜单路径**: 系统管理 → 用户行为统计
**主要数据表**: `bxw_user_stats`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| date | 统计日期 | DATE | 日期选择器 | 统计的日期 |
| login_count | 登录次数 | INTEGER | 只读 | 当日登录次数 |
| writing_count | 写作次数 | INTEGER | 只读 | 当日写作任务数 |
| rewrite_count | 降重次数 | INTEGER | 只读 | 当日降重任务数 |
| check_count | 查重次数 | INTEGER | 只读 | 当日查重任务数 |
| export_count | 导出次数 | INTEGER | 只读 | 当日导出次数 |
| credits_consumed | 消耗积分 | INTEGER | 只读 | 当日消耗积分 |
| online_duration | 在线时长 | INTEGER | 只读 | 在线时长(分钟) |
| createtime | 创建时间 | DATETIME | 只读 | 记录创建时间 |
| updatetime | 更新时间 | DATETIME | 只读 | 记录更新时间 |

#### 字段关联关系
- `user_id` → `bxw_user.id` (多对一，必填)

### 4. 系统统计
**菜单路径**: 系统管理 → 系统统计
**主要数据表**: `bxw_system_stats`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| date | 统计日期 | DATE | 日期选择器 | 统计的日期 |
| new_users | 新增用户 | INTEGER | 只读 | 当日新注册用户数 |
| active_users | 活跃用户 | INTEGER | 只读 | 当日活跃用户数 |
| total_orders | 订单总数 | INTEGER | 只读 | 当日订单数量 |
| total_revenue | 总收入 | REAL | 只读 | 当日收入金额 |
| writing_tasks | 写作任务 | INTEGER | 只读 | 当日写作任务数 |
| rewrite_tasks | 降重任务 | INTEGER | 只读 | 当日降重任务数 |
| check_tasks | 查重任务 | INTEGER | 只读 | 当日查重任务数 |
| ai_api_calls | AI调用次数 | INTEGER | 只读 | 当日AI接口调用数 |
| ai_api_cost | AI调用成本 | REAL | 只读 | 当日AI接口成本 |
| error_count | 错误数量 | INTEGER | 只读 | 当日错误日志数 |
| createtime | 创建时间 | DATETIME | 只读 | 记录创建时间 |
| updatetime | 更新时间 | DATETIME | 只读 | 记录更新时间 |

### 5. AI使用统计
**菜单路径**: 系统管理 → AI使用统计
**主要数据表**: `bxw_ai_usage_log`

#### 显示字段与表单
| 字段名 | 中文名称 | 字段类型 | 表单类型 | 说明 |
|--------|----------|----------|----------|------|
| id | ID | INTEGER | 隐藏 | 主键，自增 |
| user_id | 用户ID | INTEGER | 用户选择器 | 关联bxw_user.id |
| ai_model_id | AI模型ID | INTEGER | 下拉选择 | 关联bxw_ai_model.id |
| task_type | 任务类型 | VARCHAR(50) | 下拉选择 | outline/writing/rewrite |
| task_id | 任务ID | INTEGER | 数字框 | 关联具体任务ID |
| prompt_tokens | 提示令牌数 | INTEGER | 只读 | 输入令牌数量 |
| completion_tokens | 完成令牌数 | INTEGER | 只读 | 输出令牌数量 |
| total_tokens | 总令牌数 | INTEGER | 只读 | 总令牌数量 |
| cost | 调用成本 | REAL | 只读 | 本次调用成本 |
| response_time | 响应时间 | INTEGER | 只读 | 响应时间(毫秒) |
| status | 调用状态 | VARCHAR(20) | 状态标签 | success/failed |
| error_message | 错误信息 | TEXT | 只读 | 失败原因 |
| createtime | 创建时间 | DATETIME | 只读 | 调用时间 |

#### 字段关联关系
- `user_id` → `bxw_user.id` (多对一，必填)
- `ai_model_id` → `bxw_ai_model.id` (多对一，必填)
- `task_id` → 根据task_type关联不同任务表的ID

---

## 📋 菜单与数据库表对应关系总览

### 完整菜单-数据表映射表

| 一级菜单 | 二级菜单 | 主要数据表 | 关联表 | 功能说明 |
|----------|----------|------------|--------|----------|
| **📄 写作中心** | 论文类型管理 | `bxw_paper_type` | - | 管理论文类型配置 |
| | 大纲模板管理 | `bxw_outline_template` | `bxw_paper_type` | 管理大纲结构模板 |
| | 提示词模板管理 | `bxw_prompt_template` | `bxw_paper_type`, `bxw_ai_model` | 管理AI提示词模板 |
| | 写作任务管理 | `bxw_paper_project` | `bxw_user`, `bxw_paper_type`, `bxw_paper_section` | 管理用户写作任务 |
| | 草稿箱管理 | `bxw_paper_project` | 同上(is_draft=1) | 管理用户草稿 |
| **🔄 降重与查重** | 降重记录管理 | `bxw_rewrite_task` | `bxw_rewrite_result`, `bxw_user`, `bxw_ai_model` | 管理降重任务和结果 |
| | 降重模型配置 | `bxw_ai_model` | - | 配置降重AI模型 |
| | 查重记录管理 | `bxw_check_task` | `bxw_check_detail`, `bxw_user`, `bxw_check_api` | 管理查重任务和报告 |
| | 查重接口配置 | `bxw_check_api` | - | 配置第三方查重接口 |
| **📁 文档导出** | 导出样式模板 | `bxw_document_template` | `bxw_paper_type` | 管理导出模板 |
| | 下载记录管理 | `bxw_export_record` | `bxw_user`, `bxw_document_template` | 管理导出下载记录 |
| | 导出任务监控 | `bxw_export_record` | `bxw_n8n_execution` | 监控导出任务状态 |
| **👤 用户中心** | 用户列表 | `bxw_user` | `bxw_user_quota` | 管理用户基础信息 |
| | VIP套餐管理 | `bxw_package` | - | 管理VIP套餐配置 |
| | 用户积分管理 | `bxw_credits_log` | `bxw_user` | 管理积分变动记录 |
| **💰 收费系统** | 订单管理 | `bxw_order` | `bxw_user`, `bxw_package`, `bxw_coupon` | 管理支付订单 |
| | 套餐配置 | `bxw_package` | - | 配置销售套餐 |
| | 充值记录 | `bxw_credits_log` | `bxw_user`, `bxw_order` | 管理充值记录 |
| | 发票管理 | `bxw_invoice` | `bxw_order`, `bxw_user` | 管理发票开具 |
| | 优惠券管理 | `bxw_coupon` | `bxw_user_coupon` | 管理优惠券 |
| **📬 通知与消息** | 系统通知记录 | `bxw_user_notification` | `bxw_user` | 管理系统通知 |
| | 消息模板管理 | `bxw_message_template` | - | 管理通知模板 |
| | 邮件配置 | ThinkAdmin内置 | - | 配置SMTP邮箱 |
| | 通知记录 | `bxw_email_log` | `bxw_user`, `bxw_message_template` | 管理邮件发送记录 |
| **🧠 系统设置** | AI模型配置 | `bxw_ai_model` | - | 配置AI模型参数 |
| | 接口密钥管理 | `bxw_ai_model`, `bxw_check_api` | - | 管理API密钥 |
| | Webhook配置 | `bxw_n8n_workflow` | - | 配置n8n工作流 |
| | 内容风控规则 | `bxw_content_filter` | - | 配置内容过滤规则 |
| | 基础参数设置 | `bxw_config` | - | 管理系统配置参数 |
| | n8n工作流配置 | `bxw_n8n_workflow` | - | 管理n8n工作流 |
| | n8n执行记录 | `bxw_n8n_execution` | `bxw_n8n_workflow`, `bxw_user` | 监控工作流执行 |
| **📊 系统管理** | 操作日志 | `bxw_operation_log` | `bxw_user` | 记录用户操作日志 |
| | 错误日志 | `bxw_error_log` | `bxw_user` | 记录系统错误日志 |
| | 用户行为统计 | `bxw_user_stats` | `bxw_user` | 统计用户行为数据 |
| | 系统统计 | `bxw_system_stats` | - | 统计系统运营数据 |
| | AI使用统计 | `bxw_ai_usage_log` | `bxw_user`, `bxw_ai_model` | 统计AI使用情况 |

---

## 数据库表关系总览

### 核心关联关系图
```
bxw_user (用户表) - 核心用户信息
├── bxw_user_quota (用户配额) - user_id
├── bxw_paper_project (论文项目) - user_id
├── bxw_rewrite_task (降重任务) - user_id
├── bxw_check_task (查重任务) - user_id
├── bxw_export_record (导出记录) - user_id
├── bxw_order (订单) - user_id
├── bxw_credits_log (积分记录) - user_id
├── bxw_user_notification (用户通知) - user_id
├── bxw_user_stats (用户统计) - user_id
├── bxw_operation_log (操作日志) - user_id
├── bxw_error_log (错误日志) - user_id
├── bxw_ai_usage_log (AI使用日志) - user_id
├── bxw_email_log (邮件日志) - user_id
└── bxw_n8n_execution (n8n执行记录) - user_id

bxw_paper_type (论文类型) - 论文分类配置
├── bxw_paper_project (论文项目) - paper_type_id
├── bxw_outline_template (大纲模板) - paper_type_id
├── bxw_prompt_template (提示词模板) - paper_type_id
└── bxw_document_template (文档模板) - paper_type_id

bxw_ai_model (AI模型) - AI服务配置
├── bxw_rewrite_task (降重任务) - ai_model_id
├── bxw_rewrite_result (降重结果) - ai_model_id
├── bxw_ai_usage_log (使用日志) - ai_model_id
└── bxw_prompt_template (提示词模板) - ai_model (字符串关联)

bxw_paper_project (论文项目) - 写作任务核心
├── bxw_paper_section (论文章节) - project_id
├── bxw_export_record (导出记录) - source_id (当source_type='paper')
└── bxw_paper_project (草稿关联) - parent_id (自关联)

bxw_order (订单) - 支付核心
├── bxw_package (套餐) - package_id
├── bxw_coupon (优惠券) - coupon_id
├── bxw_credits_log (积分记录) - related_id (当related_type='order')
└── bxw_invoice (发票) - order_id

bxw_n8n_workflow (n8n工作流) - 流程编排
└── bxw_n8n_execution (执行记录) - workflow_id

bxw_message_template (消息模板) - 通知模板
└── bxw_email_log (邮件日志) - template_id

bxw_check_api (查重接口) - 查重服务配置
└── bxw_check_task (查重任务) - check_api_id
    └── bxw_check_detail (查重详情) - task_id

bxw_rewrite_task (降重任务) - 降重服务
└── bxw_rewrite_result (降重结果) - task_id

bxw_document_template (文档模板) - 导出模板
└── bxw_export_record (导出记录) - template_id

bxw_coupon (优惠券) - 优惠券配置
└── bxw_user_coupon (用户优惠券) - coupon_id
```

### 主要业务流程表关联
1. **论文写作流程**: `bxw_user` → `bxw_paper_project` → `bxw_paper_section` → `bxw_export_record`
2. **降重流程**: `bxw_user` → `bxw_rewrite_task` → `bxw_rewrite_result`
3. **查重流程**: `bxw_user` → `bxw_check_task` → `bxw_check_detail`
4. **支付流程**: `bxw_user` → `bxw_order` → `bxw_package` → `bxw_credits_log`
5. **通知流程**: `bxw_user` → `bxw_user_notification` ← `bxw_message_template`
6. **n8n集成流程**: `bxw_n8n_workflow` → `bxw_n8n_execution` → 各业务表
7. **统计分析流程**: 各业务表 → `bxw_user_stats` / `bxw_system_stats`

---

## 🔗 重要业务规则与约束

### 用户权限与配额规则
1. **用户类型权限**:
   - 普通用户(user_type=1): 基础功能，有配额限制
   - VIP用户(user_type=2): 高级功能，配额更高
   - 企业用户(user_type=3): 全功能，无配额限制

2. **积分消耗规则**:
   - 写作任务: 根据字数消耗积分
   - 降重任务: 根据原文字数消耗积分
   - 查重任务: 固定积分消耗
   - 导出任务: 免费或少量积分

3. **配额重置规则**:
   - 日配额: 每日0点重置
   - 月配额: 每月1号重置
   - VIP用户配额更高，企业用户无限制

### 任务状态流转规则
1. **论文写作状态流转**:
   ```
   draft → outline_generating → writing → completed
                ↓                ↓         ↓
              failed ←---------failed ←-failed
                ↓                ↓         ↓
            cancelled ←------cancelled ←-cancelled
   ```

2. **降重任务状态流转**:
   ```
   pending → processing → completed
       ↓         ↓           ↓
     failed ←-failed ←----failed
   ```

3. **查重任务状态流转**:
   ```
   pending → checking → completed
       ↓        ↓          ↓
     failed ←-failed ←---failed
   ```

### 支付与订单规则
1. **订单状态规则**:
   - pending: 待支付(30分钟过期)
   - paid: 已支付(自动发货)
   - failed: 支付失败
   - refunded: 已退款
   - cancelled: 已取消

2. **优惠券使用规则**:
   - 每个优惠券有使用次数限制
   - 每个用户对同一优惠券有使用次数限制
   - 优惠券有有效期限制
   - 优惠券有最小金额门槛

3. **发票开具规则**:
   - 只能对已支付订单开具发票
   - 发票金额不能超过订单实付金额
   - 企业发票需要税号
   - 发票一旦开具不可修改

### 数据完整性约束
1. **外键约束**:
   - 所有user_id必须关联有效用户
   - 删除用户时级联删除相关数据
   - 删除论文类型时限制删除(如有关联项目)

2. **业务约束**:
   - 用户积分不能为负数
   - 任务进度必须在0-100之间
   - 订单金额必须大于0
   - VIP到期时间必须大于当前时间

3. **唯一性约束**:
   - 用户名、邮箱全局唯一
   - 订单号全局唯一
   - 优惠券代码全局唯一
   - AI模型provider+model_code组合唯一

---

## 字段类型说明

### 表单控件类型
- **文本框**: 单行文本输入
- **文本域**: 多行文本输入
- **富文本编辑器**: 支持格式化的文本编辑
- **数字框**: 数字输入，支持范围限制
- **下拉选择**: 单选下拉框
- **多选框**: 多选选择器
- **开关**: 布尔值切换
- **日期选择器**: 日期时间选择
- **文件上传**: 文件上传组件
- **用户选择器**: 用户搜索选择
- **标签输入**: 支持多标签输入
- **JSON编辑器**: JSON格式数据编辑
- **密码框**: 密码输入框
- **进度条**: 进度显示
- **状态标签**: 状态显示标签
- **JSON查看器**: 只读JSON数据展示
- **文件下载**: 文件下载链接
- **链接**: 可点击的URL链接

### 数据类型说明
- **INTEGER**: 整数类型，SQLite自增主键
- **VARCHAR(n)**: 变长字符串，最大长度n
- **TEXT**: 长文本类型，无长度限制
- **REAL**: 浮点数类型
- **DATETIME**: 日期时间类型
- **DATE**: 日期类型

### 状态值枚举说明
1. **任务状态 (status)**:
   - `draft`: 草稿
   - `outline_generating`: 大纲生成中
   - `writing`: 写作中
   - `completed`: 已完成
   - `failed`: 失败
   - `cancelled`: 已取消

2. **支付状态 (payment_status)**:
   - `pending`: 待支付
   - `paid`: 已支付
   - `failed`: 支付失败
   - `refunded`: 已退款
   - `cancelled`: 已取消

3. **用户类型 (user_type)**:
   - `1`: 普通用户
   - `2`: VIP用户
   - `3`: 企业用户

4. **套餐类型 (package.type)**:
   - `credits`: 积分套餐
   - `vip`: VIP套餐
   - `combo`: 组合套餐

---

## 索引优化建议

### 高频查询索引
1. **用户相关**: `user_id` + `status` + `createtime`
2. **任务状态**: `status` + `createtime` 
3. **类型筛选**: `type` + `status`
4. **时间范围**: `createtime`, `updatetime`
5. **关联查询**: 外键字段组合索引

### 复合索引示例
```sql
-- 用户任务状态时间查询
CREATE INDEX idx_user_task_status_time ON bxw_paper_project (user_id, status, createtime);

-- AI使用统计查询  
CREATE INDEX idx_ai_usage_model_time ON bxw_ai_usage_log (ai_model_id, createtime);

-- 用户统计活跃度查询
CREATE INDEX idx_user_stats_activity ON bxw_user_stats (user_id, date, login_count);
```

---

## 数据完整性约束

### CHECK约束示例
```sql
-- 用户积分非负
CONSTRAINT chk_credits CHECK (credits >= 0)

-- 任务进度范围
CONSTRAINT chk_progress CHECK (progress >= 0 AND progress <= 100)

-- 订单金额合理性
CONSTRAINT chk_order_prices CHECK (original_price >= 0 AND final_price >= 0)

-- 状态值枚举
CONSTRAINT chk_status CHECK (status IN ('draft', 'writing', 'completed', 'failed'))
```

### 外键关系（可选启用）
```sql
-- 论文项目关联用户
ALTER TABLE bxw_paper_project ADD CONSTRAINT fk_paper_project_user
    FOREIGN KEY (user_id) REFERENCES bxw_user(id) ON DELETE CASCADE;

-- 论文项目关联类型  
ALTER TABLE bxw_paper_project ADD CONSTRAINT fk_paper_project_type
    FOREIGN KEY (paper_type_id) REFERENCES bxw_paper_type(id) ON DELETE RESTRICT;
```

---

## 🚀 实施建议

### 开发优先级建议
1. **第一阶段 - 核心功能**:
   - 用户管理系统 (bxw_user, bxw_user_quota)
   - 论文类型和模板管理 (bxw_paper_type, bxw_outline_template)
   - AI模型配置 (bxw_ai_model)
   - 基础写作功能 (bxw_paper_project)

2. **第二阶段 - 业务扩展**:
   - 降重查重功能 (bxw_rewrite_task, bxw_check_task)
   - 支付订单系统 (bxw_order, bxw_package)
   - 文档导出功能 (bxw_export_record, bxw_document_template)

3. **第三阶段 - 运营支持**:
   - 通知消息系统 (bxw_message_template, bxw_user_notification)
   - 统计分析功能 (bxw_user_stats, bxw_system_stats)
   - n8n工作流集成 (bxw_n8n_workflow, bxw_n8n_execution)

### 数据库设计建议
1. **索引策略**:
   - 为高频查询字段建立索引
   - 使用复合索引优化多条件查询
   - 定期分析索引使用情况

2. **分区策略**:
   - 日志表按时间分区 (按月或按季度)
   - 统计表按日期分区
   - 大表考虑水平分区

3. **缓存策略**:
   - 配置类数据使用Redis缓存
   - 用户会话信息缓存
   - 热点数据缓存

### 安全性建议
1. **数据安全**:
   - 敏感字段加密存储 (API密钥、密码等)
   - 定期备份数据库
   - 实施数据访问权限控制

2. **接口安全**:
   - API接口限流
   - 请求签名验证
   - SQL注入防护

3. **业务安全**:
   - 用户操作日志记录
   - 异常行为监控
   - 内容风控过滤

### 性能优化建议
1. **查询优化**:
   - 避免N+1查询问题
   - 使用分页查询大数据集
   - 合理使用JOIN和子查询

2. **缓存优化**:
   - 页面缓存
   - 数据库查询结果缓存
   - 静态资源CDN加速

3. **异步处理**:
   - 耗时任务异步处理 (通过n8n)
   - 消息队列处理高并发
   - 定时任务处理统计数据

---

## 维护建议

### 定期维护任务
1. **数据库维护**:
   - 完整性检查: `PRAGMA integrity_check;`
   - 查询优化: `PRAGMA optimize;`
   - 统计信息更新: `ANALYZE;`
   - 数据库文件优化: `VACUUM;`

2. **数据清理**:
   - 清理过期日志数据 (保留3-6个月)
   - 清理临时文件和缓存
   - 归档历史统计数据
   - 清理失效的n8n执行记录

3. **备份策略**:
   - 每日增量备份
   - 每周全量备份
   - 异地备份存储
   - 定期恢复测试

### 监控指标
1. **性能监控**:
   - 数据库响应时间
   - 慢查询统计
   - 索引命中率
   - 连接池使用率

2. **业务监控**:
   - 用户活跃度
   - 任务成功率
   - AI接口调用成功率
   - 支付成功率

3. **系统监控**:
   - 服务器资源使用率
   - 数据库文件大小增长
   - 错误日志数量
   - 异常用户行为

### 扩展性考虑
1. **水平扩展**:
   - 读写分离
   - 数据库分片
   - 微服务拆分
   - 负载均衡

2. **功能扩展**:
   - 插件化架构
   - API开放平台
   - 第三方集成
   - 多语言支持

---

## 📊 总结

本文档详细梳理了AI论文写作平台的菜单功能与数据库表对应关系，包括：

### 核心模块覆盖
- ✅ **7个主要功能模块**: 写作中心、降重查重、文档导出、用户中心、收费系统、通知消息、系统设置
- ✅ **35个数据表**: 涵盖用户管理、业务流程、支付订单、统计分析等全业务场景
- ✅ **完整字段说明**: 每个表的字段类型、表单控件、业务含义详细说明
- ✅ **关联关系梳理**: 表间外键关系、业务流程关联清晰明确

### 技术特点
- 🔧 **基于ThinkAdmin**: 利用成熟框架快速搭建管理后台
- 🔄 **集成n8n工作流**: 复杂业务流程可视化编排
- 📊 **完善的统计分析**: 用户行为、系统运营、AI使用全方位统计
- 🔒 **安全性设计**: 数据完整性约束、业务规则验证、权限控制

### 实施价值
- 📋 **开发指导**: 为开发团队提供详细的数据库设计参考
- 🎯 **需求对照**: 菜单功能与数据表一一对应，需求实现清晰
- 🔧 **维护支持**: 提供数据库维护、性能优化、扩展建议
- 📈 **业务支撑**: 支持完整的AI论文写作平台商业化运营

本文档可作为系统开发、测试、运维的重要参考资料，确保平台功能完整性和数据一致性。

---

*文档版本: v1.0*
*创建时间: 2025-01-03*
*适用系统: AI论文写作平台 (ThinkAdmin + n8n 架构)*
