AI 论文写作 N8N 工作流设计
1. 流程概览
用户提交 → 写作任务数据库 → (生成大纲流程) → 修改保存 → (生成正文流程)
  → If 控制节点（查重/去AI化/润色/参考文献）
  → 数据库写入 → 文件生成（Word/PDF） → 云存储 → 结果回传

2. 节点流程设计
流程 A：生成大纲

Trigger（API/Webhook）

接收来自 FastAdmin 的写作请求（主题、关键词、模型选择、用户ID）。

Database Node（写作任务入库）

写入 fa_writing_task，状态：待生成大纲。

AI Node（生成大纲）

调用指定 AI 模型生成论文大纲。

Database Node（大纲结果保存）

更新任务状态：大纲已生成，保存大纲 JSON/文本。

Return Node

将大纲返回给 FastAdmin，供用户修改。

流程 B：生成正文

（用户确认大纲 → 触发此流程）

Trigger（API/Webhook）

接收用户提交的大纲（已修改版本）、模型选择、勾选的功能（查重/润色/去AI化/参考文献）。

Database Node（任务状态更新）

更新任务为 待生成正文。

AI Node（生成正文）

根据大纲生成完整论文正文。

If Node（查重）

✅ 如果用户勾选了「自动查重」 → 进入查重节点

❌ 否则跳过

查重节点

API Node（查重服务） 调用第三方查重 API

If Node（重复率检测）

若重复率高 → 回到「AI Node（自动修改）」 → 循环生成修订版 → 再次查重

若重复率合格 → 继续流程

If Node（去 AI 化处理）

调用「AI 去AI化改写」服务，减少 AI 风格特征。

If Node（润色）

调用 AI 模型进行学术语言润色与格式优化。

If Node（参考文献生成）

调用 AI 或第三方 API，根据正文内容自动补充参考文献条目。

Database Node（正文结果保存）

将最终论文写入 fa_writing_task_result 表

更新状态：论文已生成。

File Generation Node（文档生成）

根据数据库中配置的模板（Word/PDF 模板）生成论文文档。

Cloud Storage Node

上传文件至云存储（阿里云OSS / AWS S3 / 本地存储）。

保存文件路径到数据库。

Return Node

返回文件下载链接与任务结果给 FastAdmin。

3. 数据库交互设计

每个阶段都写入数据库：

fa_writing_task：存任务进度（待大纲/大纲已生成/待正文/已完成等）

fa_writing_task_result：存阶段结果（大纲、正文、查重报告、参考文献）

fa_file_records：存文件生成与存储信息

这样后台可以实时追踪任务执行情况，前端可展示进度条。

4. 灵活扩展点

通过 If Node 灵活控制查重 / 去AI化 / 润色 / 参考文献，用户可自由选择。

可以增加 Webhook Node，在论文生成完成后自动通知用户（邮件/系统通知）。

可加 队列监控节点，防止任务卡死或失败。