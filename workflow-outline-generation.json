{"name": "AI论文大纲生成流程-标准版", "nodes": [{"parameters": {"httpMethod": "POST", "path": "generate-outline", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "接收写作请求", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "outline-generation-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "check-required-fields", "leftValue": "={{ $json.topic && $json.user_id }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "validate-input", "name": "验证输入参数", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"operation": "insert", "table": "fa_writing_task", "columns": "user_id, topic, keywords, ai_model, status, created_at", "additionalFields": {"mode": "defineBelow", "values": {"user_id": "={{ $('webhook-trigger').item.json.user_id }}", "topic": "={{ $('webhook-trigger').item.json.topic }}", "keywords": "={{ $('webhook-trigger').item.json.keywords || '' }}", "ai_model": "={{ $('webhook-trigger').item.json.ai_model || 'deepseek-chat' }}", "status": "待生成大纲", "created_at": "={{ new Date().toISOString() }}"}}}, "id": "db-insert-task", "name": "写作任务入库", "type": "n8n-nodes-base.mysql", "typeVersion": 2.4, "position": [680, 200], "credentials": {"mysql": {"id": "mysql-credentials", "name": "MySQL Database"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "use-deepseek", "leftValue": "={{ $('webhook-trigger').item.json.ai_model === 'deepseek' || !$('webhook-trigger').item.json.ai_model }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "check-ai-model", "name": "选择AI模型", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"model": "deepseek-chat", "messages": [{"role": "system", "content": "你是一个专业的学术论文写作助手。请根据用户提供的主题和关键词，生成一个详细的论文大纲。大纲应包含：\n1. 论文标题\n2. 摘要要点（研究背景、目的、方法、结果、结论）\n3. 引言（研究背景、问题陈述、研究意义）\n4. 主体章节（至少3-4个章节，每章包含2-3个小节）\n5. 结论与展望\n6. 参考文献说明\n\n请以结构化的JSON格式返回结果，便于后续处理。"}, {"role": "user", "content": "论文主题：{{ $('webhook-trigger').item.json.topic }}\n关键词：{{ $('webhook-trigger').item.json.keywords || '无' }}\n学科领域：{{ $('webhook-trigger').item.json.field || '通用' }}\n论文类型：{{ $('webhook-trigger').item.json.paper_type || '学术论文' }}\n\n请生成详细的论文大纲。"}], "options": {"temperature": 0.7, "max_tokens": 2000}}, "id": "deepseek-generate-outline", "name": "DeepSeek生成大纲", "type": "n8n-nodes-deepseek.deepseek", "typeVersion": 1, "position": [1120, 100], "credentials": {"deepseekApi": {"id": "deepseek-credentials", "name": "DeepSeek API"}}}, {"parameters": {"url": "https://api.openai.com/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.openAiApi.apiKey }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "bodyParameters": {"parameters": [{"name": "model", "value": "={{ $('webhook-trigger').item.json.ai_model || 'gpt-3.5-turbo' }}"}, {"name": "messages", "value": [{"role": "system", "content": "你是一个专业的学术论文写作助手。请根据用户提供的主题和关键词，生成一个详细的论文大纲。大纲应包含：\n1. 论文标题\n2. 摘要要点（研究背景、目的、方法、结果、结论）\n3. 引言（研究背景、问题陈述、研究意义）\n4. 主体章节（至少3-4个章节，每章包含2-3个小节）\n5. 结论与展望\n6. 参考文献说明\n\n请以结构化的JSON格式返回结果，便于后续处理。"}, {"role": "user", "content": "论文主题：{{ $('webhook-trigger').item.json.topic }}\n关键词：{{ $('webhook-trigger').item.json.keywords || '无' }}\n学科领域：{{ $('webhook-trigger').item.json.field || '通用' }}\n论文类型：{{ $('webhook-trigger').item.json.paper_type || '学术论文' }}\n\n请生成详细的论文大纲。"}]}, {"name": "temperature", "value": 0.7}, {"name": "max_tokens", "value": 2000}]}}, "id": "openai-generate-outline", "name": "OpenAI生成大纲", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 300], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"jsCode": "// 合并来自不同AI模型的结果\nconst items = $input.all();\nlet outlineContent = '';\nlet aiModel = '';\n\n// 检查是否有DeepSeek结果\nconst deepseekResult = items.find(item => item.json && item.json.choices);\nif (deepseekResult) {\n  outlineContent = deepseekResult.json.choices[0].message.content;\n  aiModel = 'deepseek';\n} else {\n  // 检查是否有OpenAI结果\n  const openaiResult = items.find(item => item.json && item.json.choices);\n  if (openaiResult) {\n    outlineContent = openaiResult.json.choices[0].message.content;\n    aiModel = 'openai';\n  }\n}\n\n// 获取任务ID\nconst taskId = items.find(item => item.json && item.json.insertId)?.json.insertId;\n\nreturn [{\n  json: {\n    task_id: taskId,\n    outline_content: outlineContent,\n    ai_model_used: aiModel,\n    generated_at: new Date().toISOString()\n  }\n}];"}, "id": "merge-results", "name": "合并AI结果", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"operation": "update", "table": "fa_writing_task", "updateKey": "id", "columnToMatchOn": "id", "valueToMatchOn": "={{ $json.task_id }}", "columnsUi": {"columnToUpdateValues": [{"column": "status", "value": "大纲已生成"}, {"column": "outline_content", "value": "={{ $json.outline_content }}"}, {"column": "ai_model_used", "value": "={{ $json.ai_model_used }}"}, {"column": "updated_at", "value": "={{ $json.generated_at }}"}]}}, "id": "db-update-outline", "name": "保存大纲结果", "type": "n8n-nodes-base.mysql", "typeVersion": 2.4, "position": [1560, 200], "credentials": {"mysql": {"id": "mysql-credentials", "name": "MySQL Database"}}}, {"parameters": {"respondWith": "json", "responseBody": {"success": true, "task_id": "={{ $('db-insert-task').item.json.insertId }}", "outline": "={{ $('ai-generate-outline').item.json.choices[0].message.content }}", "message": "大纲生成成功，请确认后继续生成正文"}}, "id": "return-response", "name": "返回大纲结果", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 300]}], "connections": {"接收写作请求": {"main": [[{"node": "写作任务入库", "type": "main", "index": 0}]]}, "写作任务入库": {"main": [[{"node": "AI生成大纲", "type": "main", "index": 0}]]}, "AI生成大纲": {"main": [[{"node": "保存大纲结果", "type": "main", "index": 0}]]}, "保存大纲结果": {"main": [[{"node": "返回大纲结果", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "outline-generation-workflow", "tags": ["AI写作", "论文大纲"]}