{"name": "AI论文大纲生成流程", "nodes": [{"parameters": {"httpMethod": "POST", "path": "generate-outline", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "接收写作请求", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "outline-generation-webhook"}, {"parameters": {"operation": "insert", "table": "fa_writing_task", "columns": "user_id, topic, keywords, ai_model, status, created_at", "additionalFields": {"mode": "defineBelow", "values": {"user_id": "={{ $json.user_id }}", "topic": "={{ $json.topic }}", "keywords": "={{ $json.keywords }}", "ai_model": "={{ $json.ai_model }}", "status": "待生成大纲", "created_at": "={{ new Date().toISOString() }}"}}}, "id": "db-insert-task", "name": "写作任务入库", "type": "n8n-nodes-base.mysql", "typeVersion": 2.4, "position": [460, 300], "credentials": {"mysql": {"id": "mysql-credentials", "name": "MySQL Database"}}}, {"parameters": {"model": "={{ $json.ai_model || 'gpt-4' }}", "messages": {"messageType": "multipleMessages", "messages": [{"role": "system", "content": "你是一个专业的学术论文写作助手。请根据用户提供的主题和关键词，生成一个详细的论文大纲。大纲应包含：1. 标题 2. 摘要要点 3. 引言 4. 主体章节（至少3个） 5. 结论 6. 参考文献说明。请以JSON格式返回结果。"}, {"role": "user", "content": "论文主题：{{ $json.topic }}\n关键词：{{ $json.keywords }}\n\n请生成详细的论文大纲。"}]}, "options": {"temperature": 0.7, "maxTokens": 2000}}, "id": "ai-generate-outline", "name": "AI生成大纲", "type": "n8n-nodes-base.openAi", "typeVersion": 1.3, "position": [680, 300], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"operation": "update", "table": "fa_writing_task", "updateKey": "id", "columnToMatchOn": "id", "valueToMatchOn": "={{ $('db-insert-task').item.json.insertId }}", "columnsUi": {"columnToUpdateValues": [{"column": "status", "value": "大纲已生成"}, {"column": "outline_content", "value": "={{ $json.choices[0].message.content }}"}, {"column": "updated_at", "value": "={{ new Date().toISOString() }}"}]}}, "id": "db-update-outline", "name": "保存大纲结果", "type": "n8n-nodes-base.mysql", "typeVersion": 2.4, "position": [900, 300], "credentials": {"mysql": {"id": "mysql-credentials", "name": "MySQL Database"}}}, {"parameters": {"respondWith": "json", "responseBody": {"success": true, "task_id": "={{ $('db-insert-task').item.json.insertId }}", "outline": "={{ $('ai-generate-outline').item.json.choices[0].message.content }}", "message": "大纲生成成功，请确认后继续生成正文"}}, "id": "return-response", "name": "返回大纲结果", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 300]}], "connections": {"接收写作请求": {"main": [[{"node": "写作任务入库", "type": "main", "index": 0}]]}, "写作任务入库": {"main": [[{"node": "AI生成大纲", "type": "main", "index": 0}]]}, "AI生成大纲": {"main": [[{"node": "保存大纲结果", "type": "main", "index": 0}]]}, "保存大纲结果": {"main": [[{"node": "返回大纲结果", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "outline-generation-workflow", "tags": ["AI写作", "论文大纲"]}