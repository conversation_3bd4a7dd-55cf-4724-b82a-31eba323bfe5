# 📋 AI论文写作平台 - 后台菜单结构（Markdown版）

> 架构：ThinkAdmin（菜单管理）  
> 说明：按功能模块划分，便于权限配置和模块开发

---

## 📄 写作中心

- 📁 论文类型管理  
  _用于定义论文的类型，如毕业论文、期刊论文、项目建议书等_

- 📁 大纲模板管理  
  _维护各类论文的章节结构模板，如引言、方法、结论_

- 📁 提示词模板管理  
  _管理写作过程中使用的 AI 提示词，适配不同模型_

- 📁 正文模板管理  
  _设置生成正文的格式样式、段落间距、引用规则_

- 📁 写作任务管理  
  _查看用户提交的写作任务（包含正文和大纲任务，页面里做区分），支持重试、状态跟踪_

- 📁 草稿箱管理  
  _用户草稿版本的查看、编辑与恢复（包含正文和大纲草稿，页面里做区分）_

---

## 🔄 降重与查重

- 📁 降重记录管理  
  _展示用户提交的降重记录，支持对比原文与改写结果_

- 📁 降重模型配置  
  _配置降重所使用的 AI 模型及其优先级、负载方式_

- 📁 查重记录管理  
  _查看查重任务状态、结果报告与重复率预估_

- 📁 查重接口配置  
  _配置维普、知网、万方等查重服务的接口和凭据_

---

## 📁 文档导出

- 📁 导出样式模板  
  _配置 DOCX、PDF 的导出模板、封面格式、字体_

- 📁 下载记录管理  
  _用户导出行为记录，便于内容溯源与权限控制_

- 📁 导出任务监控  
  _查看导出流程的状态、是否失败、是否打包成功_

---

## 👤 用户中心

- 📁 用户列表  
  _基础信息、VIP等级、配额、最近登录时间_

- 📁 VIP 套餐管理  
  _定义各套餐的价格、字数上限、权限范围_

- 📁 用户积分管理  
  _记录用户充值与消耗积分的明细_


---

## 💰 收费系统

- 📁 订单管理  
  _查看和管理所有付费订单，含状态、金额_

- 📁 套餐配置  
  _编辑套餐销售内容、定价策略_

- 📁 充值记录  
  _用户充值记录日志，支持导出_

- 📁 发票管理  
  _电子发票的申请、开具与审核_


---

## 📬 通知与消息

- 📁 系统通知记录  
  _系统向用户推送的所有通知消息_

- 📁 消息模板管理  
  _配置任务完成、查重成功等自动通知的模板内容_

- 📁 邮件配置  
  _设置 SMTP 邮箱参数与测试发送_

- 📁 通知记录  
  _所有用户收到通知的状态与历史_

---

## 🧠 写作系统相关设置

- 📁 AI模型配置  
  _配置 GPT-4、Claude、文心一言等的 API 接入_
- 📁 接口密钥管理  
  _维护所有外部服务接口所需的 Token、Key 等_

- 📁 Webhook 配置  
  _用于对接 n8n、查重平台等系统的回调地址_

- 📁 内容风控规则  
  _定义敏感词、内容违规判断标准_

- 📁 基础参数设置  
  _全局配置项，如最大字数、任务超时时间_

## 🧠 系统管理（thinkadmin内置功能）
……



## ✅ 附加说明

- 所有菜单支持角色权限绑定，可通过 ThinkAdmin 权限控制器实现分组管控；
- 图标建议使用 Layui Icon 或自定义 Iconfont；
- 每个菜单建议添加 `node` 节点值用于路由/权限标识，例如 `write/type/index`。
