{"name": "AI论文写作流程-标准节点版", "nodes": [{"parameters": {"httpMethod": "POST", "path": "paper-generation", "responseMode": "responseNode", "options": {}}, "id": "webhook-start", "name": "接收请求", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "paper-generation-webhook"}, {"parameters": {"url": "https://api.openai.com/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.openAiApi.apiKey }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "bodyParameters": {"parameters": [{"name": "model", "value": "={{ $json.ai_model || 'gpt-3.5-turbo' }}"}, {"name": "messages", "value": [{"role": "system", "content": "你是一个专业的学术论文写作助手。请根据用户提供的主题和要求生成完整的学术论文，包含标题、摘要、引言、主体内容、结论和参考文献。"}, {"role": "user", "content": "论文主题：{{ $json.topic }}\n关键词：{{ $json.keywords }}\n字数要求：{{ $json.word_count || '5000字' }}\n特殊要求：{{ $json.requirements || '无' }}\n\n请生成完整的学术论文。"}]}, {"name": "temperature", "value": 0.7}, {"name": "max_tokens", "value": 4000}]}}, "id": "openai-request", "name": "OpenAI生成论文", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300], "credentials": {"openAiApi": {"id": "openai-api-key", "name": "OpenAI API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "enable-polish", "leftValue": "={{ $('webhook-start').item.json.enable_polish }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "check-polish", "name": "检查是否润色", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"url": "https://api.openai.com/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.openAiApi.apiKey }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "bodyParameters": {"parameters": [{"name": "model", "value": "={{ $('webhook-start').item.json.ai_model || 'gpt-3.5-turbo' }}"}, {"name": "messages", "value": [{"role": "system", "content": "请对以下论文内容进行学术语言润色，提升表达的准确性和专业性，确保符合学术写作规范。"}, {"role": "user", "content": "{{ $('openai-request').item.json.choices[0].message.content }}"}]}, {"name": "temperature", "value": 0.6}, {"name": "max_tokens", "value": 4000}]}}, "id": "polish-request", "name": "润色处理", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 200], "credentials": {"openAiApi": {"id": "openai-api-key", "name": "OpenAI API"}}}, {"parameters": {"operation": "insert", "table": "fa_writing_results", "columns": "topic, original_content, polished_content, created_at", "additionalFields": {"mode": "defineBelow", "values": {"topic": "={{ $('webhook-start').item.json.topic }}", "original_content": "={{ $('openai-request').item.json.choices[0].message.content }}", "polished_content": "={{ $('polish-request').item.json.choices[0].message.content || null }}", "created_at": "={{ new Date().toISOString() }}"}}}, "id": "save-to-db", "name": "保存到数据库", "type": "n8n-nodes-base.mysql", "typeVersion": 2.4, "position": [1120, 300], "credentials": {"mysql": {"id": "mysql-db", "name": "MySQL Database"}}}, {"parameters": {"respondWith": "json", "responseBody": {"success": true, "message": "论文生成完成", "data": {"topic": "={{ $('webhook-start').item.json.topic }}", "original_content": "={{ $('openai-request').item.json.choices[0].message.content }}", "polished_content": "={{ $('polish-request').item.json.choices[0].message.content || null }}", "word_count": "={{ $('openai-request').item.json.choices[0].message.content.length }}", "generated_at": "={{ new Date().toISOString() }}", "record_id": "={{ $('save-to-db').item.json.insertId }}"}}}, "id": "return-result", "name": "返回结果", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 300]}], "connections": {"接收请求": {"main": [[{"node": "OpenAI生成论文", "type": "main", "index": 0}]]}, "OpenAI生成论文": {"main": [[{"node": "检查是否润色", "type": "main", "index": 0}]]}, "检查是否润色": {"main": [[{"node": "润色处理", "type": "main", "index": 0}], [{"node": "保存到数据库", "type": "main", "index": 0}]]}, "润色处理": {"main": [[{"node": "保存到数据库", "type": "main", "index": 0}]]}, "保存到数据库": {"main": [[{"node": "返回结果", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "standard-paper-workflow", "tags": ["标准节点", "AI写作", "兼容版"]}