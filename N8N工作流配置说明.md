# N8N AI论文写作工作流配置说明

## 文件说明

本项目包含三个N8N工作流配置文件：

### 1. workflow-outline-generation.json
**功能：** AI论文大纲生成流程
**用途：** 接收用户的写作请求，生成论文大纲供用户确认

**流程步骤：**
1. 接收写作请求（主题、关键词、AI模型、用户ID）
2. 写作任务入库（fa_writing_task表）
3. AI生成大纲
4. 保存大纲结果到数据库
5. 返回大纲给用户确认

**API端点：** `/generate-outline`
**请求参数：**
```json
{
  "user_id": "用户ID",
  "topic": "论文主题",
  "keywords": "关键词",
  "ai_model": "AI模型名称（可选，默认gpt-4）"
}
```

### 2. workflow-content-generation.json
**功能：** AI论文正文生成流程（完整版）
**用途：** 根据确认的大纲生成完整论文，支持查重、去AI化、润色、参考文献等功能

**流程步骤：**
1. 接收正文生成请求（包含确认的大纲和功能选项）
2. 更新任务状态为"待生成正文"
3. AI生成论文正文
4. 条件分支处理：
   - 查重检测（可选）
   - 重复率过高时自动修改
   - 去AI化处理（可选）
   - 学术润色（可选）
   - 参考文献生成（可选）
5. 保存论文结果到数据库
6. 生成Word文档
7. 上传到云存储
8. 保存文件记录
9. 返回最终结果

**API端点：** `/generate-content`
**请求参数：**
```json
{
  "task_id": "任务ID",
  "outline": "确认的大纲内容",
  "ai_model": "AI模型名称",
  "features": ["plagiarism_check", "ai_detection", "polish", "references"]
}
```

### 3. workflow-simple-demo.json
**功能：** AI论文写作演示流程（简化版）
**用途：** 快速演示和测试，一步生成完整论文

**流程步骤：**
1. 接收演示请求
2. AI生成完整论文
3. 可选润色处理
4. 返回结果

**API端点：** `/demo-paper`
**请求参数：**
```json
{
  "topic": "论文主题",
  "keywords": "关键词",
  "word_count": "字数要求（可选）",
  "enable_polish": true
}
```

## 数据库表结构

### fa_writing_task（写作任务表）
```sql
CREATE TABLE fa_writing_task (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  topic VARCHAR(500) NOT NULL,
  keywords VARCHAR(200),
  ai_model VARCHAR(50),
  status VARCHAR(50),
  outline_content TEXT,
  selected_features JSON,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

### fa_writing_task_result（任务结果表）
```sql
CREATE TABLE fa_writing_task_result (
  id INT PRIMARY KEY AUTO_INCREMENT,
  task_id INT NOT NULL,
  content LONGTEXT,
  plagiarism_report JSON,
  references TEXT,
  status VARCHAR(50),
  created_at TIMESTAMP
);
```

### fa_file_records（文件记录表）
```sql
CREATE TABLE fa_file_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  task_id INT NOT NULL,
  file_name VARCHAR(255),
  file_path VARCHAR(500),
  file_size BIGINT,
  file_type VARCHAR(50),
  storage_type VARCHAR(50),
  created_at TIMESTAMP
);
```

## 配置要求

### 1. 凭据配置
在N8N中需要配置以下凭据：

- **mysql-credentials**: MySQL数据库连接
- **openai-credentials**: OpenAI API密钥
- **plagiarism-credentials**: 查重服务API密钥
- **aws-credentials**: AWS S3存储凭据
- **word-credentials**: Microsoft Word API凭据（可选）

### 2. 环境变量
```env
MYSQL_HOST=数据库主机
MYSQL_PORT=3306
MYSQL_DATABASE=数据库名
MYSQL_USERNAME=用户名
MYSQL_PASSWORD=密码

OPENAI_API_KEY=OpenAI API密钥
PLAGIARISM_API_KEY=查重服务API密钥

AWS_ACCESS_KEY_ID=AWS访问密钥
AWS_SECRET_ACCESS_KEY=AWS秘密密钥
AWS_REGION=AWS区域
AWS_S3_BUCKET=S3存储桶名称
```

## 部署步骤

1. **导入工作流**
   - 在N8N界面中导入JSON配置文件
   - 配置所需的凭据信息

2. **数据库准备**
   - 创建相应的数据库表
   - 确保数据库连接正常

3. **API服务配置**
   - 配置OpenAI API密钥
   - 配置查重服务API（如需要）
   - 配置云存储服务

4. **测试工作流**
   - 使用简化版演示流程进行测试
   - 验证各个功能模块正常工作

5. **生产部署**
   - 激活完整版工作流
   - 配置监控和日志记录

## 使用说明

### 基本流程
1. 用户通过FastAdmin提交写作请求
2. 调用大纲生成API获取论文大纲
3. 用户确认并修改大纲
4. 调用正文生成API生成完整论文
5. 系统返回文件下载链接

### 功能特性
- **灵活的AI模型选择**：支持多种AI模型
- **模块化处理**：用户可选择需要的功能模块
- **实时状态跟踪**：数据库记录任务执行状态
- **文件管理**：自动生成文档并上传到云存储
- **错误处理**：包含重试机制和错误恢复

## 扩展建议

1. **增加更多AI模型支持**
2. **添加邮件通知功能**
3. **实现队列管理和任务监控**
4. **支持更多文档格式（PDF、LaTeX等）**
5. **添加用户权限和配额管理**
