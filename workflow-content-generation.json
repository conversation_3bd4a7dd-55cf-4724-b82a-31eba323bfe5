{"name": "AI论文正文生成流程", "nodes": [{"parameters": {"httpMethod": "POST", "path": "generate-content", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger-content", "name": "接收正文生成请求", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "content-generation-webhook"}, {"parameters": {"operation": "update", "table": "fa_writing_task", "updateKey": "id", "columnToMatchOn": "id", "valueToMatchOn": "={{ $json.task_id }}", "columnsUi": {"columnToUpdateValues": [{"column": "status", "value": "待生成正文"}, {"column": "outline_content", "value": "={{ $json.outline }}"}, {"column": "selected_features", "value": "={{ JSON.stringify($json.features) }}"}, {"column": "updated_at", "value": "={{ new Date().toISOString() }}"}]}}, "id": "db-update-status", "name": "更新任务状态", "type": "n8n-nodes-base.mysql", "typeVersion": 2.4, "position": [460, 300], "credentials": {"mysql": {"id": "mysql-credentials", "name": "MySQL Database"}}}, {"parameters": {"model": "={{ $json.ai_model || 'gpt-4' }}", "messages": {"messageType": "multipleMessages", "messages": [{"role": "system", "content": "你是一个专业的学术论文写作助手。请根据提供的大纲生成完整的论文正文。要求：1. 学术语言规范 2. 逻辑清晰 3. 内容充实 4. 格式标准 5. 字数适中（8000-12000字）"}, {"role": "user", "content": "请根据以下大纲生成完整的论文正文：\n\n{{ $json.outline }}"}]}, "options": {"temperature": 0.8, "maxTokens": 4000}}, "id": "ai-generate-content", "name": "AI生成正文", "type": "n8n-nodes-base.openAi", "typeVersion": 1.3, "position": [680, 300], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "plagiarism-check", "leftValue": "={{ $('webhook-trigger-content').item.json.features.includes('plagiarism_check') }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "if-plagiarism-check", "name": "是否查重", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"url": "https://api.plagiarism-checker.com/check", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.plagiarismApi.token }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $('ai-generate-content').item.json.choices[0].message.content }}"}, {"name": "language", "value": "zh"}]}}, "id": "plagiarism-api", "name": "查重API调用", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 200], "credentials": {"plagiarismApi": {"id": "plagiarism-credentials", "name": "Plagiarism API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "high-similarity", "leftValue": "={{ $json.similarity_rate }}", "rightValue": 30, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "id": "if-high-similarity", "name": "重复率检测", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"model": "={{ $('webhook-trigger-content').item.json.ai_model || 'gpt-4' }}", "messages": {"messageType": "multipleMessages", "messages": [{"role": "system", "content": "请对以下论文内容进行改写，降低重复率，保持学术性和逻辑性，确保内容质量不降低。"}, {"role": "user", "content": "原文：{{ $('ai-generate-content').item.json.choices[0].message.content }}\n\n查重报告：{{ $('plagiarism-api').item.json }}\n\n请进行改写以降低重复率。"}]}, "options": {"temperature": 0.9, "maxTokens": 4000}}, "id": "ai-rewrite-content", "name": "AI自动修改", "type": "n8n-nodes-base.openAi", "typeVersion": 1.3, "position": [1560, 100], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "ai-detection", "leftValue": "={{ $('webhook-trigger-content').item.json.features.includes('ai_detection') }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "if-ai-detection", "name": "是否去AI化", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 400]}, {"parameters": {"model": "={{ $('webhook-trigger-content').item.json.ai_model || 'gpt-4' }}", "messages": {"messageType": "multipleMessages", "messages": [{"role": "system", "content": "请对以下AI生成的论文内容进行人性化改写，减少AI特征，使其更符合人类写作风格，保持学术性和专业性。"}, {"role": "user", "content": "{{ $('ai-generate-content').item.json.choices[0].message.content }}"}]}, "options": {"temperature": 0.8, "maxTokens": 4000}}, "id": "ai-humanize", "name": "去AI化处理", "type": "n8n-nodes-base.openAi", "typeVersion": 1.3, "position": [1340, 500], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "polish", "leftValue": "={{ $('webhook-trigger-content').item.json.features.includes('polish') }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "if-polish", "name": "是否润色", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 600]}, {"parameters": {"model": "={{ $('webhook-trigger-content').item.json.ai_model || 'gpt-4' }}", "messages": {"messageType": "multipleMessages", "messages": [{"role": "system", "content": "请对以下论文内容进行学术语言润色和格式优化，提升表达的准确性和专业性，确保符合学术写作规范。"}, {"role": "user", "content": "{{ $('ai-generate-content').item.json.choices[0].message.content }}"}]}, "options": {"temperature": 0.6, "maxTokens": 4000}}, "id": "ai-polish", "name": "学术润色", "type": "n8n-nodes-base.openAi", "typeVersion": 1.3, "position": [1340, 700], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "references", "leftValue": "={{ $('webhook-trigger-content').item.json.features.includes('references') }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "if-references", "name": "是否生成参考文献", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 800]}, {"parameters": {"model": "={{ $('webhook-trigger-content').item.json.ai_model || 'gpt-4' }}", "messages": {"messageType": "multipleMessages", "messages": [{"role": "system", "content": "请根据论文内容生成相关的参考文献列表，格式符合学术规范（APA或GB/T 7714格式），包含作者、标题、期刊、年份等完整信息。"}, {"role": "user", "content": "论文内容：{{ $('ai-generate-content').item.json.choices[0].message.content }}\n\n请生成相关的参考文献列表。"}]}, "options": {"temperature": 0.5, "maxTokens": 2000}}, "id": "ai-references", "name": "生成参考文献", "type": "n8n-nodes-base.openAi", "typeVersion": 1.3, "position": [1340, 900], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"operation": "insert", "table": "fa_writing_task_result", "columns": "task_id, content, plagiarism_report, references, status, created_at", "additionalFields": {"mode": "defineBelow", "values": {"task_id": "={{ $('webhook-trigger-content').item.json.task_id }}", "content": "={{ $('ai-generate-content').item.json.choices[0].message.content }}", "plagiarism_report": "={{ $('plagiarism-api').item.json || null }}", "references": "={{ $('ai-references').item.json.choices[0].message.content || null }}", "status": "论文已生成", "created_at": "={{ new Date().toISOString() }}"}}}, "id": "db-save-result", "name": "保存论文结果", "type": "n8n-nodes-base.mysql", "typeVersion": 2.4, "position": [1560, 800], "credentials": {"mysql": {"id": "mysql-credentials", "name": "MySQL Database"}}}, {"parameters": {"operation": "update", "table": "fa_writing_task", "updateKey": "id", "columnToMatchOn": "id", "valueToMatchOn": "={{ $('webhook-trigger-content').item.json.task_id }}", "columnsUi": {"columnToUpdateValues": [{"column": "status", "value": "已完成"}, {"column": "updated_at", "value": "={{ new Date().toISOString() }}"}]}}, "id": "db-update-final-status", "name": "更新最终状态", "type": "n8n-nodes-base.mysql", "typeVersion": 2.4, "position": [1780, 800], "credentials": {"mysql": {"id": "mysql-credentials", "name": "MySQL Database"}}}, {"parameters": {"fileName": "={{ 'paper_' + $('webhook-trigger-content').item.json.task_id + '_' + Date.now() + '.txt' }}", "fileContent": "=标题：{{ $('webhook-trigger-content').item.json.title || '学术论文' }}\n作者：{{ $('webhook-trigger-content').item.json.author || '作者' }}\n日期：{{ new Date().toLocaleDateString('zh-CN') }}\n\n正文内容：\n{{ $('ai-generate-content').item.json.choices[0].message.content }}\n\n参考文献：\n{{ $('ai-references').item.json.choices[0].message.content || '暂无' }}", "options": {"encoding": "utf8"}}, "id": "generate-text-file", "name": "生成文本文件", "type": "n8n-nodes-base.writeFile", "typeVersion": 1, "position": [2000, 700]}, {"parameters": {"url": "https://your-file-storage-api.com/upload", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.fileStorageApi.token }}"}, {"name": "Content-Type", "value": "multipart/form-data"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "file", "value": "={{ $('generate-text-file').item.binary.data }}"}, {"name": "filename", "value": "={{ $('generate-text-file').item.json.fileName }}"}, {"name": "task_id", "value": "={{ $('webhook-trigger-content').item.json.task_id }}"}]}}, "id": "upload-to-storage", "name": "上传到文件存储", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2220, 700]}, {"parameters": {"operation": "insert", "table": "fa_file_records", "columns": "task_id, file_name, file_path, file_size, file_type, storage_type, created_at", "additionalFields": {"mode": "defineBelow", "values": {"task_id": "={{ $('webhook-trigger-content').item.json.task_id }}", "file_name": "={{ $('generate-text-file').item.json.fileName }}", "file_path": "={{ $('upload-to-storage').item.json.file_url || '/local/files/' + $('generate-text-file').item.json.fileName }}", "file_size": "={{ $('generate-text-file').item.json.fileContent.length }}", "file_type": "txt", "storage_type": "http_api", "created_at": "={{ new Date().toISOString() }}"}}}, "id": "db-save-file-record", "name": "保存文件记录", "type": "n8n-nodes-base.mysql", "typeVersion": 2.4, "position": [2440, 700], "credentials": {"mysql": {"id": "mysql-credentials", "name": "MySQL Database"}}}, {"parameters": {"respondWith": "json", "responseBody": {"success": true, "task_id": "={{ $('webhook-trigger-content').item.json.task_id }}", "message": "论文生成完成", "download_url": "={{ $('upload-to-storage').item.json.file_url || '/local/files/' + $('generate-text-file').item.json.fileName }}", "file_name": "={{ $('generate-text-file').item.json.fileName }}", "plagiarism_report": "={{ $('plagiarism-api').item.json || null }}", "processing_features": "={{ $('webhook-trigger-content').item.json.features }}"}}, "id": "return-final-response", "name": "返回最终结果", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2660, 700]}], "connections": {"接收正文生成请求": {"main": [[{"node": "更新任务状态", "type": "main", "index": 0}]]}, "更新任务状态": {"main": [[{"node": "AI生成正文", "type": "main", "index": 0}]]}, "AI生成正文": {"main": [[{"node": "是否查重", "type": "main", "index": 0}]]}, "是否查重": {"main": [[{"node": "查重API调用", "type": "main", "index": 0}], [{"node": "是否去AI化", "type": "main", "index": 0}]]}, "查重API调用": {"main": [[{"node": "重复率检测", "type": "main", "index": 0}]]}, "重复率检测": {"main": [[{"node": "AI自动修改", "type": "main", "index": 0}], [{"node": "是否去AI化", "type": "main", "index": 0}]]}, "AI自动修改": {"main": [[{"node": "查重API调用", "type": "main", "index": 0}]]}, "是否去AI化": {"main": [[{"node": "去AI化处理", "type": "main", "index": 0}], [{"node": "是否润色", "type": "main", "index": 0}]]}, "去AI化处理": {"main": [[{"node": "是否润色", "type": "main", "index": 0}]]}, "是否润色": {"main": [[{"node": "学术润色", "type": "main", "index": 0}], [{"node": "是否生成参考文献", "type": "main", "index": 0}]]}, "学术润色": {"main": [[{"node": "是否生成参考文献", "type": "main", "index": 0}]]}, "是否生成参考文献": {"main": [[{"node": "生成参考文献", "type": "main", "index": 0}], [{"node": "保存论文结果", "type": "main", "index": 0}]]}, "生成参考文献": {"main": [[{"node": "保存论文结果", "type": "main", "index": 0}]]}, "保存论文结果": {"main": [[{"node": "更新最终状态", "type": "main", "index": 0}]]}, "更新最终状态": {"main": [[{"node": "生成文本文件", "type": "main", "index": 0}]]}, "生成文本文件": {"main": [[{"node": "上传到文件存储", "type": "main", "index": 0}]]}, "上传到文件存储": {"main": [[{"node": "保存文件记录", "type": "main", "index": 0}]]}, "保存文件记录": {"main": [[{"node": "返回最终结果", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "content-generation-workflow", "tags": ["AI写作", "论文正文"]}