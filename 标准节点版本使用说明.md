# N8N 标准节点版本使用说明

## 📋 **重写完成的配置文件**

### `workflow-outline-generation-standard.json`
**功能：** AI论文大纲生成流程（使用标准节点 + MySQL + DeepSeek）

## 🔧 **使用的节点类型**

### ✅ **N8N标准内置节点**
- `n8n-nodes-base.webhook` - Webhook触发器
- `n8n-nodes-base.if` - 条件判断节点
- `n8n-nodes-base.httpRequest` - HTTP请求节点（调用OpenAI API）
- `n8n-nodes-base.code` - JavaScript代码节点
- `n8n-nodes-base.respondToWebhook` - 响应返回节点

### 🔌 **已安装的扩展节点**
- `n8n-nodes-base.mysql` - MySQL数据库节点
- `n8n-nodes-deepseek.deepseek` - DeepSeek AI节点

## 🚀 **流程设计特点**

### 1. **输入验证**
- 验证必需参数：`user_id` 和 `topic`
- 可选参数：`keywords`, `ai_model`, `field`, `paper_type`
- 参数不完整时返回友好的错误信息

### 2. **智能AI模型选择**
- **默认使用DeepSeek**：成本更低，中文支持更好
- **可选OpenAI**：通过参数 `ai_model` 指定其他模型
- 支持模型：`deepseek`, `gpt-3.5-turbo`, `gpt-4` 等

### 3. **数据库集成**
- 任务创建时自动入库
- 实时更新任务状态
- 保存AI模型使用记录
- 完整的时间戳记录

### 4. **结果合并处理**
- 使用JavaScript代码节点智能合并不同AI的结果
- 自动识别成功的AI调用
- 统一的数据格式输出

## 📝 **API接口说明**

### **端点：** `POST /webhook/generate-outline`

### **请求参数：**
```json
{
  "user_id": 123,                    // 必需：用户ID
  "topic": "人工智能在教育中的应用",    // 必需：论文主题
  "keywords": "AI, 教育, 机器学习",    // 可选：关键词
  "ai_model": "deepseek",            // 可选：AI模型（默认deepseek）
  "field": "教育技术",               // 可选：学科领域
  "paper_type": "学术论文"           // 可选：论文类型
}
```

### **成功响应：**
```json
{
  "success": true,
  "task_id": 12345,
  "outline": "详细的论文大纲内容...",
  "ai_model_used": "deepseek",
  "generated_at": "2024-01-15T10:30:00.000Z",
  "message": "大纲生成成功，请确认后继续生成正文"
}
```

### **错误响应：**
```json
{
  "success": false,
  "error": "输入参数不完整",
  "message": "请提供必需的参数：user_id 和 topic",
  "required_fields": ["user_id", "topic"],
  "optional_fields": ["keywords", "ai_model", "field", "paper_type"]
}
```

## 🛠️ **配置要求**

### 1. **数据库配置**
确保MySQL数据库中存在以下表：

```sql
CREATE TABLE fa_writing_task (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  topic VARCHAR(500) NOT NULL,
  keywords VARCHAR(200),
  ai_model VARCHAR(50),
  status VARCHAR(50),
  outline_content LONGTEXT,
  ai_model_used VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. **凭据配置**
在N8N中配置以下凭据：

#### **MySQL数据库凭据**
```json
{
  "name": "mysql-credentials",
  "type": "mysql",
  "data": {
    "host": "localhost",
    "port": 3306,
    "database": "your_database",
    "username": "your_username",
    "password": "your_password"
  }
}
```

#### **DeepSeek API凭据**
```json
{
  "name": "deepseek-credentials",
  "type": "deepseekApi",
  "data": {
    "apiKey": "your-deepseek-api-key"
  }
}
```

#### **OpenAI API凭据（可选）**
```json
{
  "name": "openai-credentials",
  "type": "openAiApi",
  "data": {
    "apiKey": "your-openai-api-key"
  }
}
```

## 🎯 **优势特点**

### 1. **兼容性强**
- ✅ 只使用标准节点和已安装的节点
- ✅ 无需安装额外的社区节点
- ✅ 避免节点版本冲突问题

### 2. **成本优化**
- 💰 默认使用DeepSeek（成本更低）
- 💰 支持多种AI模型选择
- 💰 智能的模型切换机制

### 3. **错误处理**
- 🛡️ 完善的输入验证
- 🛡️ 友好的错误提示
- 🛡️ 自动的异常恢复

### 4. **数据完整性**
- 📊 完整的任务生命周期跟踪
- 📊 详细的执行日志记录
- 📊 支持任务状态查询

## 🚀 **部署步骤**

### 1. **导入工作流**
```bash
# 在N8N界面中导入配置文件
workflow-outline-generation-standard.json
```

### 2. **配置凭据**
- 设置MySQL数据库连接
- 配置DeepSeek API密钥
- （可选）配置OpenAI API密钥

### 3. **创建数据库表**
- 执行上述SQL创建表结构
- 确保数据库连接正常

### 4. **测试工作流**
```bash
# 使用curl测试API
curl -X POST http://your-n8n-domain/webhook/generate-outline \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 1,
    "topic": "测试论文主题",
    "keywords": "测试, 关键词"
  }'
```

### 5. **激活工作流**
- 在N8N界面中激活工作流
- 监控执行日志确保正常运行

## 📈 **性能优化建议**

1. **数据库优化**
   - 为 `user_id` 和 `status` 字段添加索引
   - 定期清理历史数据

2. **API调用优化**
   - 设置合理的超时时间
   - 实现重试机制

3. **监控告警**
   - 监控API调用成功率
   - 设置数据库连接告警

## 🔍 **故障排除**

### 常见问题：
1. **DeepSeek节点报错** → 检查API密钥和网络连接
2. **MySQL连接失败** → 验证数据库配置和权限
3. **参数验证失败** → 确认请求格式和必需字段

### 调试方法：
1. 查看N8N执行日志
2. 检查数据库记录
3. 验证API响应格式

这个标准节点版本完全避免了节点安装问题，可以直接在您的N8N环境中使用！
